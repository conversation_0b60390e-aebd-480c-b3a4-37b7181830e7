import React, { useState, useEffect } from 'react';
import '../styles/MainLayout.css';

const MainLayout = ({ children, activeTab, setActiveTab }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isProjectionsDropdownOpen, setIsProjectionsDropdownOpen] = useState(false);

  const handleTabClick = (tab) => {
    setActiveTab(tab);
    setIsMobileMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };
  
  const toggleProjectionsDropdown = (e) => {
    e.stopPropagation();
    setIsProjectionsDropdownOpen(!isProjectionsDropdownOpen);
  };
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setIsProjectionsDropdownOpen(false);
    };
    
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // Handle projection sub-navigation
  const handleProjectionSubNav = (subTab, e) => {
    e.stopPropagation();
    handleTabClick('projections');
    
    // Use setTimeout to ensure the hash change happens after the tab change
    setTimeout(() => {
      // Update URL hash to include both the main tab and the sub-tab
      window.history.pushState(null, '', `#projections#${subTab}`);
      
      // Dispatch a hashchange event to ensure the Projections component detects it
      const hashChangeEvent = new Event('hashchange');
      window.dispatchEvent(hashChangeEvent);
    }, 10);
    
    setIsProjectionsDropdownOpen(false);
  };

  return (
    <div className="main-layout">
      <header className="main-header">
        <div className="header-container">
          <div className="logo-container">
            <div className="logo">
              <span className="logo-icon">💰</span>
              <div className="logo-text">
                <h1>Finance Dashboard</h1>
                <p>Sales Projection Management System</p>
              </div>
            </div>
            <button className="mobile-menu-toggle" onClick={toggleMobileMenu}>
              <span className="menu-icon">{isMobileMenuOpen ? '✕' : '☰'}</span>
            </button>
          </div>

          <nav className={`main-nav ${isMobileMenuOpen ? 'mobile-open' : ''}`}>
            <ul className="nav-list">
              <li className={activeTab === 'dashboard' ? 'active' : ''}>
                <button onClick={() => handleTabClick('dashboard')}>
                  <span className="nav-icon">📊</span>
                  <span className="nav-text">Dashboard</span>
                </button>
              </li>
              <li className={activeTab === 'projections' ? 'active' : ''}>
                <div className="nav-dropdown" onClick={(e) => e.stopPropagation()}>
                  <button 
                    onClick={toggleProjectionsDropdown} 
                    className="dropdown-toggle"
                  >
                    <span className="nav-icon">💼</span>
                    <span className="nav-text">Sales Projections</span>
                    <span className="dropdown-arrow">▼</span>
                  </button>
                  {isProjectionsDropdownOpen && (
                    <div className="dropdown-menu">
                      <button onClick={(e) => handleProjectionSubNav('form', e)}>
                        <span className="dropdown-icon">➕</span>
                        <span>Add New Projection</span>
                      </button>
                      <button onClick={(e) => handleProjectionSubNav('table', e)}>
                        <span className="dropdown-icon">📋</span>
                        <span>View All Projections</span>
                      </button>
                      <button onClick={(e) => handleProjectionSubNav('analytics', e)}>
                        <span className="dropdown-icon">📊</span>
                        <span>Projection Analytics</span>
                      </button>
                    </div>
                  )}
                </div>
              </li>
              <li className={activeTab === 'reports' ? 'active' : ''}>
                <button onClick={() => handleTabClick('reports')}>
                  <span className="nav-icon">📈</span>
                  <span className="nav-text">Reports</span>
                </button>
              </li>
              <li className={activeTab === 'settings' ? 'active' : ''}>
                <button onClick={() => handleTabClick('settings')}>
                  <span className="nav-icon">⚙️</span>
                  <span className="nav-text">Settings</span>
                </button>
              </li>
            </ul>
          </nav>

          <div className="user-profile">
            <div className="profile-avatar">
              <span>JS</span>
            </div>
            <div className="profile-info">
              <span className="profile-name">John Smith</span>
              <span className="profile-role">Financial Analyst</span>
            </div>
          </div>
        </div>
      </header>

      <main className="main-content">
        <div className="content-container">
          {children}
        </div>
      </main>

      <footer className="main-footer">
        <div className="footer-container">
          <p>&copy; 2025 Finance Dashboard. All rights reserved.</p>
          <div className="footer-links">
            <a href="#">Privacy Policy</a>
            <a href="#">Terms of Service</a>
            <a href="#">Contact</a>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default MainLayout;