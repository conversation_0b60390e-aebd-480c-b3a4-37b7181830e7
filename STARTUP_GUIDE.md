# Finance Dashboard - Startup Guide

## Quick Start Scripts

I've created several batch files to make starting the Finance Dashboard super easy:

### 🚀 **Recommended: Complete Setup and Start**
```
setup-and-start.bat
```
**What it does:**
- Installs all dependencies (frontend & backend)
- Tests database connection
- Starts both servers in separate windows
- Perfect for first-time setup

### ⚡ **Quick Start (if already set up)**
```
start-both.bat
```
**What it does:**
- Starts both frontend and backend servers
- Opens in separate command windows
- Use this for daily development

### 🔧 **Individual Server Control**

**Backend Only:**
```
start-backend.bat
```
- Starts only the backend server on port 5001

**Frontend Only:**
```
start-frontend.bat
```
- Starts only the frontend server on port 3000+

### 🛑 **Stop All Servers**
```
stop-servers.bat
```
**What it does:**
- Stops all running Node.js processes
- Kills servers on ports 3000-3010 and 5001
- Use when you want to completely stop everything

### 💻 **PowerShell Alternative**
```
start-both.ps1
```
- PowerShell version of start-both.bat
- Better for some Windows configurations

## Usage Instructions

### First Time Setup:
1. **Double-click** `setup-and-start.bat`
2. Wait for installation and setup to complete
3. Two new windows will open (backend & frontend)
4. Open your browser to the URL shown in the frontend window

### Daily Development:
1. **Double-click** `start-both.bat`
2. Two windows will open immediately
3. Start coding!

### When Done:
1. **Double-click** `stop-servers.bat` OR
2. Close the server windows OR
3. Press `Ctrl+C` in each server window

## What Each Server Does

### Backend Server (Port 5001)
- **API Endpoints:** http://localhost:5001/api/sales-projections
- **Health Check:** http://localhost:5001/health
- **Database:** SQLite file management
- **CRUD Operations:** Create, Read, Update, Delete sales projections

### Frontend Server (Port 3000+)
- **Web Interface:** http://localhost:3000 (or next available port)
- **Sales Form:** Add new projections
- **Data Table:** View, edit, delete existing projections
- **Responsive Design:** Works on desktop and mobile

## Troubleshooting

### Port Already in Use
- Run `stop-servers.bat` first
- Or change ports in the configuration files

### Dependencies Not Installing
- Make sure you have Node.js installed
- Run as Administrator if needed
- Check your internet connection

### Database Issues
- The database is created automatically
- Run `cd server && npm run clear-db` to reset
- Run `cd server && npm run test-db` to verify

### Can't See the Application
- Check the frontend window for the actual port number
- Try http://localhost:3000, http://localhost:3001, or http://localhost:3002
- Make sure both servers are running

## File Structure
```
Finance dashboard/
├── setup-and-start.bat     ← Complete setup (recommended)
├── start-both.bat          ← Quick start both servers
├── start-backend.bat       ← Backend only
├── start-frontend.bat      ← Frontend only
├── stop-servers.bat        ← Stop everything
├── start-both.ps1          ← PowerShell version
├── src/                    ← Frontend code
├── server/                 ← Backend code
└── server/database/        ← SQLite database
```

## Tips

1. **First time?** Use `setup-and-start.bat`
2. **Daily use?** Use `start-both.bat`
3. **Development?** Keep both server windows open
4. **Finished?** Use `stop-servers.bat` or close windows
5. **Issues?** Check the server windows for error messages

Happy coding! 🎉
