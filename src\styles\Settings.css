/* Settings Styles */
.settings-container {
  width: 100%;
}

.settings-header {
  margin-bottom: 2rem;
}

.settings-header h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.settings-header p {
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Settings Layout */
.settings-content {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 1.5rem;
}

/* Sidebar Styles */
.settings-sidebar {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  padding: 1rem;
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.875rem 1rem;
  border-radius: var(--border-radius);
  background-color: transparent;
  border: none;
  text-align: left;
  font-size: 0.95rem;
  color: var(--text-secondary);
  transition: var(--transition);
  margin-bottom: 0.5rem;
}

.sidebar-item:hover {
  background-color: var(--bg-light);
  color: var(--text-primary);
}

.sidebar-item.active {
  background-color: rgba(67, 97, 238, 0.1);
  color: var(--primary-color);
  font-weight: 500;
}

.sidebar-icon {
  font-size: 1.25rem;
}

/* Main Content Styles */
.settings-main {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 2rem;
}

.settings-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.section-description {
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

/* Profile Card */
.profile-card {
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 1.25rem;
}

.profile-avatar {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
}

.profile-details {
  flex: 1;
}

.profile-details h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.profile-details p {
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.profile-meta {
  font-size: 0.85rem;
  color: var(--text-light);
}

.edit-profile-btn {
  padding: 0.6rem 1.25rem;
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
  font-weight: 500;
  transition: var(--transition);
}

.edit-profile-btn:hover {
  background-color: var(--bg-light);
  color: var(--text-primary);
}

/* Form Styles */
.settings-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.25rem;
}

.form-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.form-group input,
.form-group select {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  color: var(--text-primary);
  background-color: var(--bg-white);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1);
}

.form-group input[readonly] {
  background-color: var(--bg-light);
  cursor: not-allowed;
}

.form-actions {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}

.save-btn {
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition);
}

.save-btn:hover {
  background-color: var(--primary-dark);
}

/* Notification Settings */
.notification-settings {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.notification-group {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1.5rem;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.notification-header h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.notification-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.notification-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.notification-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
}

.option-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.option-icon {
  font-size: 1.25rem;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:focus + .toggle-slider {
  box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

input:disabled + .toggle-slider {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Appearance Settings */
.appearance-settings {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.theme-selector,
.color-selector,
.density-selector {
  margin-bottom: 1.5rem;
}

.theme-selector h4,
.color-selector h4,
.density-selector h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.theme-options {
  display: flex;
  gap: 1.5rem;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
}

.theme-preview {
  width: 100px;
  height: 70px;
  border-radius: var(--border-radius);
  border: 2px solid transparent;
  transition: var(--transition);
}

.theme-option.active .theme-preview {
  border-color: var(--primary-color);
}

.light-theme {
  background-color: #f8f9fa;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.dark-theme {
  background-color: #212529;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.system-theme {
  background: linear-gradient(to right, #f8f9fa 50%, #212529 50%);
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.color-options {
  display: flex;
  gap: 1rem;
}

.color-option {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  border: 2px solid transparent;
}

.color-option.active {
  border-color: var(--text-primary);
  transform: scale(1.1);
}

.density-options {
  display: flex;
  gap: 1rem;
}

.density-option {
  padding: 0.6rem 1.25rem;
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
  font-weight: 500;
  transition: var(--transition);
}

.density-option:hover {
  background-color: var(--bg-light);
}

.density-option.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Placeholder Content */
.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.placeholder-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.placeholder-content p {
  color: var(--text-secondary);
  max-width: 400px;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .settings-content {
    grid-template-columns: 1fr;
  }
  
  .settings-sidebar {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .sidebar-item {
    width: auto;
    margin-bottom: 0;
  }
}

@media (max-width: 768px) {
  .settings-main {
    padding: 1.5rem;
  }
  
  .profile-header {
    flex-direction: column;
    text-align: center;
  }
  
  .profile-details {
    margin-bottom: 1rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .theme-options,
  .color-options,
  .density-options {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .settings-sidebar {
    flex-direction: column;
  }
  
  .sidebar-item {
    width: 100%;
  }
}