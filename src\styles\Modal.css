.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: #718096;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close-btn:hover {
  background: #f7fafc;
  color: #2d3748;
}

.modal-form {
  padding: 2rem;
}

.modal-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

/* Currency input styling for modals */
.modal-form .currency-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.modal-form .currency-symbol {
  position: absolute;
  left: 1rem;
  color: #4a5568;
  font-weight: 600;
  font-size: 1rem;
  z-index: 1;
  pointer-events: none;
}

.modal-form .currency-input {
  padding-left: 2.5rem !important;
}

.modal-form .currency-input-wrapper .form-input.error {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.cancel-btn {
  background: transparent;
  color: #718096;
  border: 2px solid #e2e8f0;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.cancel-btn:hover:not(:disabled) {
  border-color: #cbd5e0;
  color: #4a5568;
  transform: translateY(-1px);
}

.cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Delete Modal Specific Styles */
.delete-modal {
  max-width: 500px;
}

.delete-modal-body {
  padding: 2rem;
}

.delete-warning {
  color: #e53e3e;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 1.1rem;
}

.record-summary {
  background: #f7fafc;
  border-radius: 10px;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.record-summary h3 {
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.record-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  color: #718096;
  font-weight: 500;
  min-width: 100px;
}

.detail-value {
  color: #2d3748;
  font-weight: 600;
  text-align: right;
  flex: 1;
}

.delete-confirm-btn {
  background: linear-gradient(135deg, #e53e3e, #c53030);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.delete-confirm-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(229, 62, 62, 0.4);
}

.delete-confirm-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 0.5rem;
  }

  .modal-content {
    border-radius: 15px;
    max-height: 95vh;
  }

  .modal-header {
    padding: 1.5rem 1.5rem 1rem;
  }

  .modal-header h2 {
    font-size: 1.25rem;
  }

  .modal-form {
    padding: 1.5rem;
  }

  .modal-form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .modal-actions {
    flex-direction: column-reverse;
  }

  .save-btn,
  .cancel-btn,
  .delete-confirm-btn {
    width: 100%;
  }

  .delete-modal-body {
    padding: 1.5rem;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .detail-value {
    text-align: left;
  }
}

@media (max-width: 480px) {
  .modal-header {
    padding: 1rem;
  }

  .modal-form {
    padding: 1rem;
  }

  .delete-modal-body {
    padding: 1rem;
  }
}
