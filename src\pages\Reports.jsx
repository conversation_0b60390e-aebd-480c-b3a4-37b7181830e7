import React, { useState } from 'react';
import '../styles/Reports.css';

const Reports = () => {
  const [activeReportType, setActiveReportType] = useState('summary');
  
  // Sample report templates
  const reportTemplates = [
    { id: 'summary', name: 'Summary Report', icon: '📊', description: 'Overview of all sales projections' },
    { id: 'vertical', name: 'Vertical Analysis', icon: '📈', description: 'Breakdown by business vertical' },
    { id: 'client', name: 'Client Report', icon: '👥', description: 'Analysis by client performance' },
    { id: 'trend', name: 'Trend Analysis', icon: '📉', description: 'Historical trends and forecasts' },
    { id: 'performance', name: 'Performance Report', icon: '🎯', description: 'Actual vs projected performance' },
    { id: 'custom', name: 'Custom Report', icon: '⚙️', description: 'Build your own custom report' }
  ];
  
  // Sample saved reports
  const savedReports = [
    { id: 1, name: 'Q2 Summary Report', type: 'summary', date: '2025-04-15', user: '<PERSON>' },
    { id: 2, name: 'Technology Vertical Analysis', type: 'vertical', date: '2025-04-10', user: '<PERSON>' },
    { id: 3, name: 'Healthcare Client Performance', type: 'client', date: '2025-04-05', user: '<PERSON>' },
    { id: 4, name: 'Annual Trend Analysis', type: 'trend', date: '2025-03-28', user: 'Emily Davis' }
  ];

  return (
    <div className="reports-container">
      <div className="reports-header">
        <h2>Reports</h2>
        <p>Generate and manage financial reports</p>
      </div>

      <div className="reports-content">
        <div className="reports-sidebar">
          <div className="sidebar-section">
            <h3>Report Templates</h3>
            <div className="template-list">
              {reportTemplates.map(template => (
                <button 
                  key={template.id}
                  className={`template-item ${activeReportType === template.id ? 'active' : ''}`}
                  onClick={() => setActiveReportType(template.id)}
                >
                  <span className="template-icon">{template.icon}</span>
                  <div className="template-info">
                    <span className="template-name">{template.name}</span>
                    <span className="template-desc">{template.description}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          <div className="sidebar-section">
            <h3>Saved Reports</h3>
            <div className="saved-reports-list">
              {savedReports.map(report => (
                <div key={report.id} className="saved-report-item">
                  <div className="report-icon">
                    {reportTemplates.find(t => t.id === report.type)?.icon || '📄'}
                  </div>
                  <div className="report-info">
                    <span className="report-name">{report.name}</span>
                    <span className="report-meta">
                      {new Date(report.date).toLocaleDateString()} • {report.user}
                    </span>
                  </div>
                  <div className="report-actions">
                    <button className="action-icon" title="View Report">👁️</button>
                    <button className="action-icon" title="Download Report">💾</button>
                  </div>
                </div>
              ))}
            </div>
            <button className="view-all-reports-btn">View All Saved Reports</button>
          </div>
        </div>

        <div className="report-builder">
          <div className="builder-header">
            <h3>{reportTemplates.find(t => t.id === activeReportType)?.name || 'Report'} Builder</h3>
            <div className="builder-actions">
              <button className="action-btn secondary-btn">
                <span>💾</span> Save Template
              </button>
              <button className="action-btn primary-btn">
                <span>📊</span> Generate Report
              </button>
            </div>
          </div>

          <div className="builder-content">
            <div className="builder-section">
              <h4>Report Parameters</h4>
              <div className="parameter-grid">
                <div className="parameter-field">
                  <label>Report Title</label>
                  <input type="text" placeholder="Enter report title" />
                </div>
                <div className="parameter-field">
                  <label>Date Range</label>
                  <select>
                    <option value="current-month">Current Month</option>
                    <option value="current-quarter">Current Quarter</option>
                    <option value="current-year">Current Year</option>
                    <option value="custom">Custom Range</option>
                  </select>
                </div>
                <div className="parameter-field">
                  <label>Vertical</label>
                  <select>
                    <option value="all">All Verticals</option>
                    <option value="technology">Technology</option>
                    <option value="healthcare">Healthcare</option>
                    <option value="finance">Finance</option>
                    <option value="retail">Retail</option>
                  </select>
                </div>
                <div className="parameter-field">
                  <label>Client</label>
                  <select>
                    <option value="all">All Clients</option>
                    <option value="tech-solutions">Tech Solutions Inc.</option>
                    <option value="healthcare-plus">Healthcare Plus</option>
                    <option value="finance-corp">Finance Corp</option>
                    <option value="retail-innovations">Retail Innovations</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="builder-section">
              <h4>Report Components</h4>
              <div className="component-list">
                <div className="component-item">
                  <input type="checkbox" id="summary-section" checked readOnly />
                  <label htmlFor="summary-section">Executive Summary</label>
                </div>
                <div className="component-item">
                  <input type="checkbox" id="charts-section" checked readOnly />
                  <label htmlFor="charts-section">Charts & Visualizations</label>
                </div>
                <div className="component-item">
                  <input type="checkbox" id="data-tables" checked readOnly />
                  <label htmlFor="data-tables">Data Tables</label>
                </div>
                <div className="component-item">
                  <input type="checkbox" id="trend-analysis" />
                  <label htmlFor="trend-analysis">Trend Analysis</label>
                </div>
                <div className="component-item">
                  <input type="checkbox" id="forecasting" />
                  <label htmlFor="forecasting">Forecasting</label>
                </div>
                <div className="component-item">
                  <input type="checkbox" id="recommendations" />
                  <label htmlFor="recommendations">Recommendations</label>
                </div>
              </div>
            </div>

            <div className="builder-section">
              <h4>Report Preview</h4>
              <div className="report-preview">
                <div className="preview-placeholder">
                  <span className="preview-icon">📊</span>
                  <p>Your report preview will appear here after you generate the report</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;