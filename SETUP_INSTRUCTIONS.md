# Finance Dashboard Setup Instructions

## Quick Start Guide

### Prerequisites Installation

#### 1. Install MySQL Server

**Windows:**
1. Download MySQL Installer from https://dev.mysql.com/downloads/installer/
2. Run the installer and choose "Developer Default"
3. Set root password during installation
4. Start MySQL service

**Alternative - Using XAMPP:**
1. Download XAMPP from https://www.apachefriends.org/
2. Install XAMPP
3. Start Apache and MySQL from XAMPP Control Panel

#### 2. Verify MySQL Installation

Open Command Prompt and run:
```bash
mysql --version
```

### Database Setup

#### Option 1: Using MySQL Command Line
```bash
# Connect to MySQL
mysql -u root -p

# Create database
CREATE DATABASE finance_dashboard;

# Use the database
USE finance_dashboard;

# Run the schema (copy content from database/schema.sql)
```

#### Option 2: Using phpMyAdmin (if using XAMPP)
1. Open http://localhost/phpmyadmin
2. Create new database named `finance_dashboard`
3. Import the `database/schema.sql` file

### Configuration

1. **Update Backend Configuration**:
   Edit `server/.env`:
   ```env
   DB_HOST=localhost
   DB_PORT=3306
   DB_USER=root
   DB_PASSWORD=your_mysql_password
   DB_NAME=finance_dashboard
   PORT=5000
   NODE_ENV=development
   ```

2. **Frontend Configuration**:
   The `.env` file is already configured:
   ```env
   VITE_API_URL=http://localhost:5000/api
   ```

### Running the Application

#### Terminal 1 - Backend Server
```bash
cd server
npm run dev
```

You should see:
```
✅ Database connected successfully
🚀 Finance Dashboard API server running on port 5000
```

#### Terminal 2 - Frontend Development Server
```bash
npm run dev
```

You should see:
```
Local:   http://localhost:3000/
```

### Testing the Setup

1. **Backend Health Check**:
   Open http://localhost:5000/health

2. **API Test**:
   Open http://localhost:5000/api/sales-projections

3. **Frontend**:
   Open http://localhost:3000

### Troubleshooting

#### MySQL Connection Issues

**Error: "Database connection failed"**

1. **Check MySQL Service**:
   - Windows: Services → MySQL80 (should be running)
   - XAMPP: MySQL should be green/running

2. **Verify Credentials**:
   ```bash
   mysql -u root -p
   ```
   Use the same password in `server/.env`

3. **Check Database Exists**:
   ```sql
   SHOW DATABASES;
   ```
   Should list `finance_dashboard`

4. **Port Conflicts**:
   - MySQL default port: 3306
   - Backend API port: 5000
   - Frontend dev port: 3000

#### Common Solutions

1. **Reset MySQL Password**:
   ```bash
   mysql -u root -p
   ALTER USER 'root'@'localhost' IDENTIFIED BY 'newpassword';
   ```

2. **Create Database Manually**:
   ```sql
   CREATE DATABASE finance_dashboard;
   USE finance_dashboard;
   -- Copy and paste content from database/schema.sql
   ```

3. **Check Firewall**:
   Ensure ports 3000, 5000, and 3306 are not blocked

### Development Workflow

1. **Start MySQL** (XAMPP or Windows Service)
2. **Start Backend** (`cd server && npm run dev`)
3. **Start Frontend** (`npm run dev`)
4. **Open Browser** (http://localhost:3000)

### Database Status

The database starts completely empty - no sample data is included. You can start adding sales projections immediately using the form.

### Next Steps

1. Fill out the sales projection form
2. Submit and see it appear in recent submissions
3. Check the database to verify data persistence
4. Test the API endpoints using a tool like Postman

### Support

If you encounter issues:
1. Check the console logs in both terminals
2. Verify MySQL is running
3. Ensure all dependencies are installed
4. Check the database connection settings
