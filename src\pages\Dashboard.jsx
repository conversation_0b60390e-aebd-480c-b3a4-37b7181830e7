import React from 'react';
import '../styles/Dashboard.css';

const Dashboard = () => {
  // Sample data for dashboard
  const summaryData = [
    { title: 'Total Projections', value: '128', icon: '📊', trend: '+12%', color: 'blue' },
    { title: 'Monthly Revenue', value: '$1.2M', icon: '💰', trend: '+8%', color: 'green' },
    { title: 'Active Projects', value: '24', icon: '🚀', trend: '+5%', color: 'purple' },
    { title: 'Conversion Rate', value: '68%', icon: '📈', trend: '+3%', color: 'orange' }
  ];

  const recentActivity = [
    { id: 1, action: 'New projection added', user: '<PERSON>', time: '2 hours ago', project: 'Tech Solutions Inc.' },
    { id: 2, action: 'Projection updated', user: '<PERSON>', time: '5 hours ago', project: 'Healthcare Plus' },
    { id: 3, action: 'Projection approved', user: '<PERSON>', time: '1 day ago', project: 'Finance Corp' },
    { id: 4, action: 'New projection added', user: '<PERSON>', time: '2 days ago', project: 'Retail Innovations' }
  ];

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <h2>Dashboard Overview</h2>
        <div className="dashboard-actions">
          <button className="action-btn refresh-btn">
            <span>↻</span> Refresh
          </button>
          <button className="action-btn export-btn">
            <span>↓</span> Export
          </button>
        </div>
      </div>

      <div className="summary-cards">
        {summaryData.map((item, index) => (
          <div key={index} className={`summary-card ${item.color}`}>
            <div className="card-icon">{item.icon}</div>
            <div className="card-content">
              <h3>{item.title}</h3>
              <div className="card-value">{item.value}</div>
              <div className="card-trend">{item.trend} from last month</div>
            </div>
          </div>
        ))}
      </div>

      <div className="dashboard-grid">
        <div className="dashboard-chart-container">
          <div className="chart-header">
            <h3>Sales Projection Trends</h3>
            <div className="chart-period">
              <button className="period-btn active">Week</button>
              <button className="period-btn">Month</button>
              <button className="period-btn">Quarter</button>
              <button className="period-btn">Year</button>
            </div>
          </div>
          <div className="chart-placeholder">
            <div className="chart-message">
              <span className="chart-icon">📊</span>
              <p>Chart visualization will appear here</p>
            </div>
          </div>
        </div>

        <div className="dashboard-activity">
          <h3>Recent Activity</h3>
          <div className="activity-list">
            {recentActivity.map(activity => (
              <div key={activity.id} className="activity-item">
                <div className="activity-icon">
                  <span>🔔</span>
                </div>
                <div className="activity-details">
                  <div className="activity-header">
                    <span className="activity-action">{activity.action}</span>
                    <span className="activity-time">{activity.time}</span>
                  </div>
                  <div className="activity-meta">
                    <span className="activity-user">{activity.user}</span>
                    <span className="activity-project">{activity.project}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <button className="view-all-btn">View All Activity</button>
        </div>
      </div>

      <div className="dashboard-footer">
        <div className="quick-actions">
          <h3>Quick Actions</h3>
          <div className="action-buttons">
            <button className="quick-action-btn">
              <span className="action-icon">➕</span>
              <span>New Projection</span>
            </button>
            <button className="quick-action-btn">
              <span className="action-icon">📊</span>
              <span>Generate Report</span>
            </button>
            <button className="quick-action-btn">
              <span className="action-icon">📤</span>
              <span>Export Data</span>
            </button>
            <button className="quick-action-btn">
              <span className="action-icon">⚙️</span>
              <span>Settings</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;