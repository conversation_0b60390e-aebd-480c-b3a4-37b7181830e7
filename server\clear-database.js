import fs from 'fs';
import path from 'path';
import { initDatabase, initSchema, executeQuery } from './config/database.js';

const clearAndRecreateDatabase = async () => {
  console.log('🗑️  Clearing database...');
  
  try {
    // Remove existing database file
    const dbPath = './database/finance_dashboard.db';
    if (fs.existsSync(dbPath)) {
      fs.unlinkSync(dbPath);
      console.log('✅ Existing database file removed');
    }
    
    // Remove database directory and recreate
    const dbDir = path.dirname(dbPath);
    if (fs.existsSync(dbDir)) {
      fs.rmSync(dbDir, { recursive: true, force: true });
      console.log('✅ Database directory removed');
    }
    
    // Recreate database directory
    fs.mkdirSync(dbDir, { recursive: true });
    console.log('✅ Database directory recreated');
    
    // Initialize fresh database
    console.log('🔄 Initializing fresh database...');
    const connected = await initDatabase();
    
    if (!connected) {
      throw new Error('Failed to connect to database');
    }
    
    const schemaInitialized = await initSchema();
    
    if (!schemaInitialized) {
      throw new Error('Failed to initialize schema');
    }
    
    // Verify empty database
    const result = await executeQuery('SELECT COUNT(*) as count FROM sales_projections');
    
    if (result.success) {
      const count = result.data[0].count;
      console.log(`✅ Database recreated successfully with ${count} records`);
      
      if (count === 0) {
        console.log('🎉 Database is completely clean and ready for use!');
      } else {
        console.log('⚠️  Warning: Database still contains data');
      }
    }
    
  } catch (error) {
    console.error('❌ Error clearing database:', error.message);
  }
};

clearAndRecreateDatabase();
