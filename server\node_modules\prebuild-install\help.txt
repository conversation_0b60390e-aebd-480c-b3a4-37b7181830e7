prebuild-install [options]

  --download    -d  [url]       (download prebuilds, no url means github)
  --target      -t  version     (version to install for)
  --runtime     -r  runtime     (Node runtime [node or electron] to build or install for, default is node)
  --path        -p  path        (make a prebuild-install here)
  --token       -T  gh-token    (github token for private repos)
  --arch            arch        (target CPU architecture, see Node OS module docs, default is current arch)
  --platform        platform    (target platform, see Node OS module docs, default is current platform)
  --tag-prefix <prefix>         (github tag prefix, default is "v")
  --force                       (always use prebuilt binaries when available)
  --build-from-source           (skip prebuild download)
  --verbose                     (log verbosely)
  --libc                        (use provided libc rather than system default)
  --debug                       (set Debug or Release configuration)
  --version                     (print prebuild-install version and exit)
