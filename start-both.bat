@echo off
echo ========================================
echo Finance Dashboard - Starting Both Servers
echo ========================================
echo.

echo This will start both backend and frontend servers in separate windows.
echo.
echo Backend will run on: http://localhost:5001
echo Frontend will run on: http://localhost:3000 (or next available port)
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

echo.
echo Starting backend server...
start "Finance Dashboard - Backend" cmd /k "start-backend.bat"

echo Waiting 3 seconds for backend to initialize...
timeout /t 3 /nobreak >nul

echo.
echo Starting frontend server...
start "Finance Dashboard - Frontend" cmd /k "start-frontend.bat"

echo.
echo ========================================
echo Both servers are starting!
echo ========================================
echo.
echo Backend: http://localhost:5001
echo Frontend: http://localhost:3000 (check the frontend window for actual port)
echo.
echo Two new command windows have opened:
echo 1. Backend Server (Finance Dashboard - Backend)
echo 2. Frontend Server (Finance Dashboard - Frontend)
echo.
echo Close those windows to stop the servers.
echo.
echo This window will close in 10 seconds...
timeout /t 10 /nobreak >nul
