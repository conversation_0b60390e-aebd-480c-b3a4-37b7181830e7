{"name": "finance-dashboard-backend", "version": "1.0.0", "description": "Backend API for Finance Dashboard Sales Projection", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1", "test-db": "node test-db.js", "clear-db": "node clear-database.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.7"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["finance", "dashboard", "sales", "projection", "mysql", "api"], "author": "", "license": "ISC"}