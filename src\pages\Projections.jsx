import React, { useState, useEffect } from 'react';
import SalesProjectionForm from '../components/SalesProjectionForm';
import SalesProjectionTable from '../components/SalesProjectionTable';
import '../styles/Projections.css';

const Projections = () => {
  const [activeTab, setActiveTab] = useState('form');
  const [dataChanged, setDataChanged] = useState(false);
  
  const handleDataChange = () => {
    setDataChanged(prev => !prev);
  };

  // Function to handle tab changes
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    // Update URL hash without triggering a full page reload
    window.history.pushState(null, '', `#projections#${tab}`);
  };

  // Check for hash in URL to set active tab
  useEffect(() => {
    const hash = window.location.hash;
    if (hash.includes('#projections#')) {
      const subTab = hash.split('#projections#')[1];
      if (['form', 'table', 'analytics'].includes(subTab)) {
        setActiveTab(subTab);
      }
    } else if (hash.includes('#')) {
      const subTab = hash.split('#')[1];
      if (['form', 'table', 'analytics'].includes(subTab)) {
        setActiveTab(subTab);
      }
    }
  }, []);

  // Listen for hash changes
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash;
      if (hash.includes('#projections#')) {
        const subTab = hash.split('#projections#')[1];
        if (['form', 'table', 'analytics'].includes(subTab)) {
          setActiveTab(subTab);
        }
      } else if (hash.includes('#')) {
        const subTab = hash.split('#')[1];
        if (['form', 'table', 'analytics'].includes(subTab)) {
          setActiveTab(subTab);
        }
      }
    };

    window.addEventListener('hashchange', handleHashChange);
    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, []);

  return (
    <div className="projections-container">
      <div className="projections-header">
        <h2>Sales Projections</h2>
        <p>Manage and track your sales projections</p>
      </div>

      {/* Add tabs navigation */}
      <div className="projections-tabs">
        <button 
          className={`tab-button ${activeTab === 'form' ? 'active' : ''}`}
          onClick={() => handleTabChange('form')}
        >
          <span className="tab-icon">➕</span>
          <span>Add New Projection</span>
        </button>
        <button 
          className={`tab-button ${activeTab === 'table' ? 'active' : ''}`}
          onClick={() => handleTabChange('table')}
        >
          <span className="tab-icon">📋</span>
          <span>View All Projections</span>
        </button>
        <button 
          className={`tab-button ${activeTab === 'analytics' ? 'active' : ''}`}
          onClick={() => handleTabChange('analytics')}
        >
          <span className="tab-icon">📊</span>
          <span>Projection Analytics</span>
        </button>
      </div>

      <div className="projections-content">
        {activeTab === 'form' && (
          <div className="projection-form-container">
            <SalesProjectionForm onDataChange={handleDataChange} />
          </div>
        )}

        {activeTab === 'table' && (
          <div className="projection-table-container">
            <SalesProjectionTable onDataChange={handleDataChange} key={dataChanged} />
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="projection-analytics-container">
            <div className="analytics-header">
              <h3>Projection Analytics</h3>
              <div className="analytics-filters">
                <select className="filter-select">
                  <option value="all">All Verticals</option>
                  <option value="technology">Technology</option>
                  <option value="healthcare">Healthcare</option>
                  <option value="finance">Finance</option>
                  <option value="retail">Retail</option>
                </select>
                <select className="filter-select">
                  <option value="year">This Year</option>
                  <option value="quarter">This Quarter</option>
                  <option value="month">This Month</option>
                  <option value="custom">Custom Range</option>
                </select>
                <button className="filter-button">Apply Filters</button>
              </div>
            </div>

            <div className="analytics-grid">
              <div className="analytics-card">
                <h4>Projections by Vertical</h4>
                <div className="chart-placeholder">
                  <span className="chart-icon">📊</span>
                  <p>Pie chart will appear here</p>
                </div>
              </div>
              <div className="analytics-card">
                <h4>Monthly Projection Trends</h4>
                <div className="chart-placeholder">
                  <span className="chart-icon">📈</span>
                  <p>Line chart will appear here</p>
                </div>
              </div>
              <div className="analytics-card">
                <h4>Top Clients by Value</h4>
                <div className="chart-placeholder">
                  <span className="chart-icon">📊</span>
                  <p>Bar chart will appear here</p>
                </div>
              </div>
              <div className="analytics-card">
                <h4>Projection Completion Rate</h4>
                <div className="chart-placeholder">
                  <span className="chart-icon">🎯</span>
                  <p>Progress chart will appear here</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Projections;