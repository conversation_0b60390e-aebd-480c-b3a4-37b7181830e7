{"version": 3, "file": "Database.js", "sourceRoot": "", "sources": ["../src/Database.ts"], "names": [], "mappings": ";;;AAGA,2CAAuC;AACvC,6CAAyC;AACzC,6CAA6C;AAG7C,uDAAkD;AAElD;;GAEG;AACH,MAAa,QAAQ;IAOnB,YAAa,MAAsB;QACjC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAA;IAChB,CAAC;IAED;;;OAGG;IACH,EAAE,CAAE,KAAa,EAAE,QAAQ;QACzB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IAC7B,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,EAAE,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,IAAI;QACF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAA;YAE5C,4FAA4F;YAC5F,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE;gBAC/C,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAA;aAC/D;YAED,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAA;aACjD;YAED,IAAI,IAAI,EAAE;gBACR,IAAI,CAAC,EAAE,GAAG,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE;oBACzC,IAAI,GAAG,EAAE;wBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;qBAChC;oBAED,OAAO,EAAE,CAAA;gBACX,CAAC,CAAC,CAAA;aACH;iBAAM;gBACL,IAAI,CAAC,EAAE,GAAG,IAAI,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;oBACnC,IAAI,GAAG,EAAE;wBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;qBAChC;oBAED,OAAO,EAAE,CAAA;gBACX,CAAC,CAAC,CAAA;aACH;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,KAAK;QACH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gBAClB,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;iBAChC;gBAED,OAAO,EAAE,CAAA;YACX,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,SAAS,CAAE,MAA+B,EAAE,KAAU;QACpD,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,MAAa,EAAE,KAAK,CAAC,CAAA;IACzC,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,GAAG,CACD,GAAoB,EACpB,GAAG,MAAa;QAEhB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,IAAA,qBAAW,EAAC,GAAG,EAAE,MAAM,CAAC,CAAA;YAEvC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,UAAU,GAAG;gBACrD,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;iBAChC;gBAED,OAAO,CAAC;oBACN,IAAI,EAAE,IAAI,qBAAS,CAAO,IAAI,CAAC,IAAI,CAAC;oBACpC,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,GAAG,CACD,GAAoB,EACpB,GAAG,MAAa;QAEhB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,IAAA,qBAAW,EAAC,GAAG,EAAE,MAAM,CAAC,CAAA;YAEvC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAO,EAAE,EAAE;gBACzD,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;iBAChC;gBAED,OAAO,CAAC,GAAG,CAAC,CAAA;YACd,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IA6CD,IAAI,CAAW,GAAoB,EAAE,GAAG,MAAa;QACnD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,QAAQ,GAA0B,MAAM,CAAC,GAAG,EAAE,CAAA;YAEpD,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;gBAC/C,MAAM,IAAI,KAAK,CACb,mEAAmE,CACpE,CAAA;aACF;YAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,EAAE,CAAA;gBAE/B,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE;oBACpC,MAAM,IAAI,KAAK,CACb,2FAA2F,CAC5F,CAAA;iBACF;gBAED,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;aACxB;YAED,MAAM,MAAM,GAAG,IAAA,qBAAW,EAAC,GAAG,EAAE,MAAM,CAAC,CAAA;YAEvC,IAAI,CAAC,EAAE,CAAC,IAAI,CACV,MAAM,CAAC,GAAG,EACV,GAAG,MAAM,CAAC,MAAM,EAChB,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACX,IAAI,GAAG,EAAE;oBACP,OAAO,QAAQ,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;iBACxC;gBAED,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;YACrB,CAAC,EACD,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBACb,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;iBAChC;gBAED,OAAO,CAAC,KAAK,CAAC,CAAA;YAChB,CAAC,CACF,CAAA;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,GAAG,CAAa,GAAoB,EAAE,GAAG,MAAa;QACpD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,IAAA,qBAAW,EAAC,GAAG,EAAE,MAAM,CAAC,CAAA;YAEvC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,IAAQ,EAAE,EAAE;gBAC1D,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;iBAChC;gBAED,OAAO,CAAC,IAAI,CAAC,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACH,IAAI,CAAE,GAAoB;QACxB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,IAAA,qBAAW,EAAC,GAAG,CAAC,CAAA;YAE/B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;gBAC7B,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;iBAChC;gBAED,OAAO,EAAE,CAAA;YACX,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACH,OAAO,CAAE,GAAoB,EAAE,GAAG,MAAa;QAC7C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,IAAA,qBAAW,EAAC,GAAG,EAAE,MAAM,CAAC,CAAA;YAEvC,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;gBAC/D,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA;iBACnB;gBAED,OAAO,CAAC,IAAI,qBAAS,CAAO,IAAI,CAAC,CAAC,CAAA;YACpC,CAAC,CAAS,CAAA;QACZ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;OAIG;IACH,aAAa,CAAE,IAAY;QACzB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;gBAChC,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;iBAChC;gBAED,OAAO,EAAE,CAAA;YACX,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAE,MAAwB;QACrC,MAAM,IAAA,iBAAO,EAAC,IAAI,EAAE,MAAM,CAAC,CAAA;IAC7B,CAAC;IAED;;OAEG;IAEH;;OAEG;IACH,SAAS;QACP,MAAM,IAAI,KAAK,CACb,mFAAmF,CACpF,CAAA;IACH,CAAC;IAED;;OAEG;IACH,WAAW;QACT,MAAM,IAAI,KAAK,CACb,qFAAqF,CACtF,CAAA;IACH,CAAC;CACF;AAvXD,4BAuXC"}