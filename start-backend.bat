@echo off
echo ========================================
echo Finance Dashboard - Starting Backend
echo ========================================
echo.

echo Checking backend dependencies...
cd server
if not exist node_modules (
    echo Installing backend dependencies...
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install backend dependencies
        pause
        exit /b 1
    )
)

echo.
echo Testing database connection...
call npm run test-db
if errorlevel 1 (
    echo WARNING: Database test failed, but continuing...
)

echo.
echo Starting backend server on port 5001...
echo Backend will be available at: http://localhost:5001
echo API endpoints at: http://localhost:5001/api/sales-projections
echo Health check at: http://localhost:5001/health
echo.
echo Press Ctrl+C to stop the server
echo ========================================

call npm run dev
