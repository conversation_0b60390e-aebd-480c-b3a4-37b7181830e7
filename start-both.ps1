# Finance Dashboard - PowerShell Startup Script
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Finance Dashboard - Starting Both Servers" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "This will start both backend and frontend servers." -ForegroundColor Yellow
Write-Host ""
Write-Host "Backend will run on: http://localhost:5001" -ForegroundColor Green
Write-Host "Frontend will run on: http://localhost:3000 (or next available port)" -ForegroundColor Green
Write-Host ""

$continue = Read-Host "Press Enter to continue or Ctrl+C to cancel"

# Function to start process in new window
function Start-ServerInNewWindow {
    param(
        [string]$Title,
        [string]$Command,
        [string]$WorkingDirectory = (Get-Location)
    )
    
    $processInfo = New-Object System.Diagnostics.ProcessStartInfo
    $processInfo.FileName = "powershell.exe"
    $processInfo.Arguments = "-NoExit -ExecutionPolicy Bypass -Command `"cd '$WorkingDirectory'; $Command`""
    $processInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Normal
    $processInfo.CreateNoWindow = $false
    
    $process = New-Object System.Diagnostics.Process
    $process.StartInfo = $processInfo
    $process.Start()
    
    return $process
}

Write-Host ""
Write-Host "Starting backend server..." -ForegroundColor Yellow

# Start backend
$backendProcess = Start-ServerInNewWindow -Title "Finance Dashboard - Backend" -Command "cd server; npm run dev" -WorkingDirectory (Get-Location)

Write-Host "Waiting 3 seconds for backend to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

Write-Host "Starting frontend server..." -ForegroundColor Yellow

# Start frontend
$frontendProcess = Start-ServerInNewWindow -Title "Finance Dashboard - Frontend" -Command "npm run dev" -WorkingDirectory (Get-Location)

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "🎉 Both servers are starting!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Backend:  http://localhost:5001" -ForegroundColor Cyan
Write-Host "Frontend: http://localhost:3000 (check the frontend window for actual port)" -ForegroundColor Cyan
Write-Host ""
Write-Host "Two new PowerShell windows have opened:" -ForegroundColor White
Write-Host "1. Backend Server" -ForegroundColor White
Write-Host "2. Frontend Server" -ForegroundColor White
Write-Host ""
Write-Host "Close those windows or press Ctrl+C in them to stop the servers." -ForegroundColor Yellow
Write-Host ""
Write-Host "This window will close in 10 seconds..." -ForegroundColor Gray

Start-Sleep -Seconds 10
