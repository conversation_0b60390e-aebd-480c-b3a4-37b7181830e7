/* Projections Styles */
.projections-container {
  width: 100%;
}

.projections-header {
  margin-bottom: 2rem;
}

.projections-header h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.projections-header p {
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Tabs */
.projections-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1rem;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.25rem;
  background-color: var(--bg-white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.tab-button:hover {
  color: var(--primary-color);
  background-color: var(--bg-subtle);
  border-color: var(--primary-light);
}

.tab-button.active {
  color: white;
  background-color: var(--primary-color);
  border-color: var(--primary-dark);
  font-weight: 600;
  box-shadow: var(--box-shadow);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid var(--primary-color);
}

.tab-icon {
  font-size: 1.25rem;
}

.tab-button.active .tab-icon {
  color: white;
}

/* Content Containers */
.projections-content {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 2rem;
}

/* Placeholder Styles */
.form-placeholder,
.table-placeholder {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
  border: 2px dashed var(--border-color);
}

.placeholder-message {
  text-align: center;
  padding: 2rem;
}

.placeholder-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 1rem;
}

.placeholder-message h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.placeholder-message p {
  color: var(--text-secondary);
}

/* Analytics Styles */
.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.analytics-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.analytics-filters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-white);
  color: var(--text-primary);
  font-size: 0.9rem;
  min-width: 150px;
}

.filter-button {
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition);
}

.filter-button:hover {
  background-color: var(--primary-dark);
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.analytics-card {
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.analytics-card h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  border: 1px dashed var(--border-color);
}

.chart-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.chart-placeholder p {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .analytics-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .projections-tabs {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .tab-button {
    width: 100%;
    justify-content: flex-start;
  }
  
  .tab-button.active::after {
    display: none;
  }
  
  .analytics-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .analytics-filters {
    width: 100%;
  }
  
  .filter-select {
    flex: 1;
  }
  
  .projections-content {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .projections-content {
    padding: 1rem;
  }
  
  .analytics-filters {
    flex-direction: column;
  }
  
  .filter-select, 
  .filter-button {
    width: 100%;
  }
}