@echo off
echo ========================================
echo Finance Dashboard - Starting Frontend
echo ========================================
echo.

echo Checking frontend dependencies...
if not exist node_modules (
    echo Installing frontend dependencies...
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install frontend dependencies
        pause
        exit /b 1
    )
)

echo.
echo Starting frontend development server...
echo Frontend will be available at: http://localhost:3000 (or next available port)
echo.
echo Press Ctrl+C to stop the server
echo ========================================

call npm run dev
