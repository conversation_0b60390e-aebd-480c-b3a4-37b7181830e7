import * as sqlite3 from 'sqlite3';
import { Statement } from './Statement';
import { Database } from './Database';
import { ISqlite, IMigrate } from './interfaces';
/**
 * Opens a database for manipulation. Most users will call this to get started.
 */
declare function open<Driver extends sqlite3.Database = sqlite3.Database, Stmt extends sqlite3.Statement = sqlite3.Statement>(config: ISqlite.Config): Promise<Database>;
export { open, Statement, Database, ISqlite, IMigrate };
