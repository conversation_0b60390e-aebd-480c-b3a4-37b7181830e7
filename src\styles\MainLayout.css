/* Main Layout Styles */
:root {
  /* Professional Color Palette */
  --primary-color: #0056b3;      /* Primary blue - more corporate */
  --primary-light: #007bff;      /* Lighter blue for hover states */
  --primary-dark: #004494;       /* Darker blue for active states */
  --secondary-color: #6c757d;    /* Neutral gray as secondary color */
  --accent-color: #28a745;       /* Green accent for success/positive elements */
  
  /* Text Colors */
  --text-primary: #212529;       /* Dark gray for primary text */
  --text-secondary: #495057;     /* Medium gray for secondary text */
  --text-light: #6c757d;         /* Light gray for tertiary text */
  
  /* Background Colors */
  --bg-light: #f8f9fa;           /* Very light gray for backgrounds */
  --bg-white: #ffffff;           /* White for cards and containers */
  --bg-dark: #343a40;            /* Dark background for contrast elements */
  --bg-subtle: #e9ecef;          /* Subtle background for hover states */
  
  /* Utility Colors */
  --border-color: #dee2e6;       /* Light gray for borders */
  --success-color: #28a745;      /* Green for success messages */
  --warning-color: #ffc107;      /* Yellow for warnings */
  --error-color: #dc3545;        /* Red for errors */
  --info-color: #17a2b8;         /* Teal for information */
  
  /* UI Elements */
  --border-radius: 4px;          /* More subtle border radius for professional look */
  --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); /* Subtle shadow */
  --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);    /* Larger shadow for modals/dropdowns */
  --transition: all 0.2s ease-in-out;                    /* Slightly faster, more subtle transitions */
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-light);
  color: var(--text-primary);
  line-height: 1.6;
}

a {
  text-decoration: none;
  color: var(--primary-color);
}

button {
  cursor: pointer;
  font-family: inherit;
}

ul {
  list-style: none;
}

/* Layout Structure */
.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header Styles */
.main-header {
  background-color: var(--bg-white);
  box-shadow: var(--box-shadow);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Logo Styles */
.logo-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-icon {
  font-size: 2rem;
}

.logo-text h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
}

.logo-text p {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Navigation Styles */
.main-nav {
  flex: 1;
  margin: 0 2rem;
}

.nav-list {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.nav-list li {
  position: relative;
}

.nav-list li button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-weight: 500;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.nav-list li button:hover {
  background-color: var(--bg-subtle);
  color: var(--primary-color);
}

.nav-list li.active button {
  background-color: rgba(0, 86, 179, 0.1);
  color: var(--primary-color);
  font-weight: 600;
}

.nav-list li.active::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 3px;
}

.nav-icon {
  font-size: 1.25rem;
}

/* User Profile Styles */
.user-profile {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.profile-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.profile-info {
  display: flex;
  flex-direction: column;
}

.profile-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--text-primary);
}

.profile-role {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--primary-color);
}

/* Main Content Styles */
.main-content {
  flex: 1;
  padding: 2rem;
}

.content-container {
  max-width: 1400px;
  margin: 0 auto;
}

/* Footer Styles */
.main-footer {
  background-color: var(--bg-white);
  border-top: 1px solid var(--border-color);
  padding: 1.5rem 0;
  margin-top: auto;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-container p {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.footer-links {
  display: flex;
  gap: 1.5rem;
}

.footer-links a {
  color: var(--text-secondary);
  font-size: 0.9rem;
  transition: var(--transition);
}

.footer-links a:hover {
  color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .header-container {
    padding: 1rem;
  }
  
  .main-nav {
    margin: 0 1rem;
  }
  
  .nav-text {
    display: none;
  }
  
  .nav-list li button {
    padding: 0.75rem;
  }
  
  .nav-icon {
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .logo-container {
    width: 100%;
  }
  
  .mobile-menu-toggle {
    display: block;
  }
  
  .main-nav {
    margin: 1rem 0 0;
    width: 100%;
    display: none;
  }
  
  .main-nav.mobile-open {
    display: block;
  }
  
  .nav-list {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .nav-list li button {
    width: 100%;
    justify-content: flex-start;
    padding: 1rem;
  }
  
  .nav-text {
    display: inline;
  }
  
  .nav-list li.active::after {
    display: none;
  }
  
  .user-profile {
    margin-top: 1rem;
    width: 100%;
    justify-content: flex-start;
  }
  
  .footer-container {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .footer-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 1rem;
  }
  
  .logo-text h1 {
    font-size: 1.25rem;
  }
  
  .logo-text p {
    font-size: 0.7rem;
  }
}

/* Dropdown Navigation Styles */
.nav-dropdown {
  position: relative;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-weight: 500;
  border-radius: var(--border-radius);
  transition: var(--transition);
  cursor: pointer;
}

.dropdown-toggle:hover {
  background-color: var(--bg-subtle);
  color: var(--primary-color);
}

.nav-list li.active .dropdown-toggle {
  background-color: rgba(0, 86, 179, 0.1);
  color: var(--primary-color);
  font-weight: 600;
}

.dropdown-arrow {
  font-size: 0.7rem;
  margin-left: 0.25rem;
  transition: transform 0.2s ease;
}

.nav-dropdown:hover .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 220px;
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-lg);
  border: 1px solid var(--border-color);
  z-index: 100;
  margin-top: 0.5rem;
  overflow: hidden;
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.dropdown-menu button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  text-align: left;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-size: 0.9rem;
  transition: var(--transition);
  border-bottom: 1px solid var(--border-color);
}

.dropdown-menu button:last-child {
  border-bottom: none;
}

.dropdown-menu button:hover {
  background-color: var(--bg-subtle);
  color: var(--primary-color);
}

.dropdown-icon {
  font-size: 1rem;
  width: 1.5rem;
  text-align: center;
  color: var(--primary-color);
}

/* Responsive styles for dropdown */
@media (max-width: 768px) {
  .dropdown-menu {
    position: static;
    width: 100%;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    box-shadow: none;
    border: 1px solid var(--border-color);
    animation: none;
  }
}