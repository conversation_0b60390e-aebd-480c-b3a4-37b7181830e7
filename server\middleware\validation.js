import Joi from 'joi';

// Sales projection validation schema
const salesProjectionSchema = Joi.object({
  employeeId: Joi.string().required().min(1).max(50).messages({
    'string.empty': 'Employee ID is required',
    'string.max': 'Employee ID must be less than 50 characters'
  }),
  
  employeeName: Joi.string().required().min(1).max(255).messages({
    'string.empty': 'Employee name is required',
    'string.max': 'Employee name must be less than 255 characters'
  }),
  
  vertical: Joi.string().required().valid(
    'technology', 'healthcare', 'finance', 'retail', 
    'manufacturing', 'education', 'government', 'other'
  ).messages({
    'any.only': 'Vertical must be one of: technology, healthcare, finance, retail, manufacturing, education, government, other'
  }),
  
  client: Joi.string().required().min(1).max(255).messages({
    'string.empty': 'Client is required',
    'string.max': 'Client name must be less than 255 characters'
  }),
  
  project: Joi.string().required().min(1).max(255).messages({
    'string.empty': 'Project is required',
    'string.max': 'Project name must be less than 255 characters'
  }),
  
  projectType: Joi.string().required().valid(
    'consulting', 'development', 'maintenance', 'support', 
    'training', 'implementation', 'migration', 'other'
  ).messages({
    'any.only': 'Project type must be one of: consulting, development, maintenance, support, training, implementation, migration, other'
  }),
  
  startDate: Joi.date().required().messages({
    'date.base': 'Start date must be a valid date'
  }),
  
  endDate: Joi.date().required().greater(Joi.ref('startDate')).messages({
    'date.base': 'End date must be a valid date',
    'date.greater': 'End date must be after start date'
  }),
  
  amount: Joi.number().required().positive().precision(2).messages({
    'number.base': 'Amount must be a number',
    'number.positive': 'Amount must be positive'
  }),
  
  amountPeriod: Joi.string().required().valid(
    'monthly', 'quarterly', 'half-yearly', 'yearly'
  ).messages({
    'any.only': 'Amount period must be one of: monthly, quarterly, half-yearly, yearly'
  })
});

// Validation middleware
export const validateSalesProjection = (req, res, next) => {
  const { error, value } = salesProjectionSchema.validate(req.body, {
    abortEarly: false,
    stripUnknown: true
  });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));

    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors
    });
  }

  req.validatedData = value;
  next();
};

// ID parameter validation
export const validateId = (req, res, next) => {
  const id = parseInt(req.params.id);
  
  if (isNaN(id) || id <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Invalid ID parameter'
    });
  }
  
  req.validatedId = id;
  next();
};

// Query parameters validation for listing
export const validateListQuery = (req, res, next) => {
  const schema = Joi.object({
    limit: Joi.number().integer().min(1).max(100).default(20),
    offset: Joi.number().integer().min(0).default(0),
    employeeId: Joi.string().optional()
  });

  const { error, value } = schema.validate(req.query);

  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Invalid query parameters',
      error: error.details[0].message
    });
  }

  req.validatedQuery = value;
  next();
};

export default {
  validateSalesProjection,
  validateId,
  validateListQuery
};
