@echo off
echo ========================================
echo Finance Dashboard - Complete Setup and Start
echo ========================================
echo.

echo This script will:
echo 1. Install all dependencies
echo 2. Test database connection
echo 3. Start both servers
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

echo.
echo ========================================
echo Step 1: Installing Frontend Dependencies
echo ========================================
echo.

if not exist node_modules (
    echo Installing frontend dependencies...
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install frontend dependencies
        pause
        exit /b 1
    )
    echo ✅ Frontend dependencies installed successfully
) else (
    echo ✅ Frontend dependencies already installed
)

echo.
echo ========================================
echo Step 2: Installing Backend Dependencies
echo ========================================
echo.

cd server
if not exist node_modules (
    echo Installing backend dependencies...
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install backend dependencies
        pause
        exit /b 1
    )
    echo ✅ Backend dependencies installed successfully
) else (
    echo ✅ Backend dependencies already installed
)

echo.
echo ========================================
echo Step 3: Testing Database Connection
echo ========================================
echo.

call npm run test-db
if errorlevel 1 (
    echo ⚠️  Database test failed, but continuing...
) else (
    echo ✅ Database connection successful
)

cd ..

echo.
echo ========================================
echo Step 4: Starting Servers
echo ========================================
echo.

echo Starting backend server...
start "Finance Dashboard - Backend" cmd /k "start-backend.bat"

echo Waiting 5 seconds for backend to initialize...
timeout /t 5 /nobreak >nul

echo Starting frontend server...
start "Finance Dashboard - Frontend" cmd /k "start-frontend.bat"

echo.
echo ========================================
echo 🎉 Setup Complete!
echo ========================================
echo.
echo ✅ Frontend dependencies installed
echo ✅ Backend dependencies installed
echo ✅ Database tested
echo ✅ Both servers starting
echo.
echo Your Finance Dashboard is now running:
echo 📊 Frontend: http://localhost:3000 (check frontend window for actual port)
echo 🔧 Backend:  http://localhost:5001
echo 🏥 Health:   http://localhost:5001/health
echo 📈 API:      http://localhost:5001/api/sales-projections
echo.
echo Two new command windows have opened for the servers.
echo Close those windows to stop the servers.
echo.
echo This window will close in 15 seconds...
timeout /t 15 /nobreak >nul
