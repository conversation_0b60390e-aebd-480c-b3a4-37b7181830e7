{"name": "react-hook-form", "description": "Performant, flexible and extensible forms library for React Hooks", "version": "7.56.4", "main": "dist/index.cjs.js", "module": "dist/index.esm.mjs", "umd:main": "dist/index.umd.js", "unpkg": "dist/index.umd.js", "jsdelivr": "dist/index.umd.js", "jsnext:main": "dist/index.esm.mjs", "source": "src/index.ts", "types": "dist/index.d.ts", "sideEffects": false, "files": ["dist", "dist/__tests__"], "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "react-server": "./dist/react-server.esm.mjs", "import": "./dist/index.esm.mjs", "require": "./dist/index.cjs.js"}}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "prepare": "husky", "prebuild": "pnpm clean", "build": "pnpm build:modern", "build:watch": "pnpm build:modern -w", "postbuild": "rimraf dist/__tests__ && node ./scripts/rollup/assert-esm-exports.mjs && node ./scripts/rollup/assert-cjs-exports.cjs", "build:modern": "rollup --bundleConfigAsCjs -c ./scripts/rollup/rollup.config.js", "build:esm": "rollup --bundleConfigAsCjs -c ./scripts/rollup/rollup.esm.config.js", "prettier:fix": "prettier --config .prettierrc --write .", "lint": "eslint '**/*.{js,ts,tsx}'", "lint:fix": "pnpm lint --fix", "type": "tsc --noEmit", "jest-preview": "jest-preview", "test": "jest --config ./scripts/jest/jest.config.js", "test:coverage": "pnpm test -- --coverage", "test:watch": "pnpm test -- --onlyChanged --watch", "test:web": "TEST_ENV=web pnpm test", "test:type": "tsd src/__typetest__", "e2e": "cypress run", "e2e:watch": "cypress open", "api-extractor": "api-extractor run --local", "api-extractor:build": "pnpm build:esm && pnpm api-extractor", "api-extractor:ci": "node scripts/apiExtractor.js", "postversion": "git push && git push origin v$npm_package_version", "prepublishOnly": "pnpm install && pnpm lint:fix && pnpm type && pnpm test && pnpm build", "bundlewatch": "pnpm build:modern && bundlewatch", "start": "pnpm build:esm && pnpm --dir ./app install && pnpm --dir ./app run dev", "csb:install": "npm i -g pnpm@8 && pnpm i"}, "keywords": ["react", "hooks", "form", "forms", "form-validation", "validation", "typescript", "react-hooks"], "repository": {"type": "git", "url": "https://github.com/react-hook-form/react-hook-form"}, "homepage": "https://www.react-hook-form.com", "author": "<PERSON><PERSON>(<PERSON>) <PERSON><PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@eslint/compat": "^1.2.8", "@microsoft/api-extractor": "^7.52.3", "@rollup/plugin-commonjs": "^26.0.3", "@rollup/plugin-node-resolve": "^15.3.1", "@rollup/plugin-terser": "^0.4.4", "@swc/core": "^1.11.20", "@swc/jest": "^0.2.37", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/react": "^19.1.1", "@types/react-dom": "^18.3.6", "@types/testing-library__jest-dom": "^5.14.9", "bundlewatch": "^0.3.3", "cypress": "^13.17.0", "eslint": "^9.24.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-cypress": "^3.6.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.1.1", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-fixed-jsdom": "^0.0.9", "jest-preview": "^0.3.1", "lint-staged": "^15.5.1", "msw": "^2.7.4", "prettier": "^3.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-test-renderer": "^18.3.1", "rimraf": "^5.0.10", "rollup": "^4.39.0", "rollup-plugin-typescript2": "^0.36.0", "tsd": "^0.31.2", "typescript": "^5.8.3", "typescript-eslint": "^7.18.0", "whatwg-fetch": "^3.6.20", "zod": "^3.24.2"}, "bundlewatch": {"files": [{"path": "./dist/index.cjs.js", "maxSize": "11.0 kB"}]}, "peerDependencies": {"react": "^16.8.0 || ^17 || ^18 || ^19"}, "lint-staged": {"*.{js,ts,tsx}": ["pnpm lint:fix", "pnpm prettier:fix"], "*.{md,json,yml}": ["prettier --write"]}, "funding": {"type": "opencollective", "url": "https://opencollective.com/react-hook-form"}, "engines": {"node": ">=18.0.0"}}