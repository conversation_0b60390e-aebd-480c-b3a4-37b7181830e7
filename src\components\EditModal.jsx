import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import Form<PERSON><PERSON> from './FormField'
import '../styles/Modal.css'

const EditModal = ({ record, onSave, onCancel }) => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const { register, handleSubmit, formState: { errors }, reset, setValue } = useForm()

  const verticalOptions = [
    { value: 'technology', label: 'Technology' },
    { value: 'healthcare', label: 'Healthcare' },
    { value: 'finance', label: 'Finance' },
    { value: 'retail', label: 'Retail' },
    { value: 'manufacturing', label: 'Manufacturing' },
    { value: 'education', label: 'Education' },
    { value: 'government', label: 'Government' },
    { value: 'other', label: 'Other' }
  ]

  const projectTypeOptions = [
    { value: 'consulting', label: 'Consulting' },
    { value: 'development', label: 'Development' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'support', label: 'Support' },
    { value: 'training', label: 'Training' },
    { value: 'implementation', label: 'Implementation' },
    { value: 'migration', label: 'Migration' },
    { value: 'other', label: 'Other' }
  ]

  const amountPeriodOptions = [
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' },
    { value: 'half-yearly', label: 'Half Yearly' },
    { value: 'yearly', label: 'Yearly' }
  ]

  useEffect(() => {
    if (record) {
      // Populate form with existing data
      setValue('employeeId', record.employeeId)
      setValue('employeeName', record.employeeName)
      setValue('vertical', record.vertical)
      setValue('client', record.client)
      setValue('project', record.project)
      setValue('projectType', record.projectType)
      setValue('startDate', record.startDate)
      setValue('endDate', record.endDate)
      setValue('amount', record.amount)
      setValue('amountPeriod', record.amountPeriod)
    }
  }, [record, setValue])

  const onSubmit = async (data) => {
    try {
      setLoading(true)
      setError('')
      await onSave(data)
    } catch (err) {
      setError(err.message || 'Failed to update record')
    } finally {
      setLoading(false)
    }
  }

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onCancel()
    }
  }

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className="modal-content">
        <div className="modal-header">
          <h2>Edit Sales Projection</h2>
          <button onClick={onCancel} className="modal-close-btn">×</button>
        </div>

        {error && (
          <div className="error-message-banner">
            ❌ {error}
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="modal-form">
          <div className="modal-form-grid">
            <FormField
              label="Employee ID"
              name="employeeId"
              register={register}
              errors={errors}
              required={true}
              placeholder="Enter employee ID"
            />

            <FormField
              label="Employee Name"
              name="employeeName"
              register={register}
              errors={errors}
              required={true}
              placeholder="Enter employee name"
            />

            <FormField
              label="Vertical"
              name="vertical"
              type="select"
              register={register}
              errors={errors}
              options={verticalOptions}
              required={true}
            />

            <FormField
              label="Client"
              name="client"
              register={register}
              errors={errors}
              required={true}
              placeholder="Enter client name"
            />

            <FormField
              label="Project"
              name="project"
              register={register}
              errors={errors}
              required={true}
              placeholder="Enter project name"
            />

            <FormField
              label="Project Type"
              name="projectType"
              type="select"
              register={register}
              errors={errors}
              options={projectTypeOptions}
              required={true}
            />

            <FormField
              label="Start Date"
              name="startDate"
              type="date"
              register={register}
              errors={errors}
              required={true}
            />

            <FormField
              label="End Date"
              name="endDate"
              type="date"
              register={register}
              errors={errors}
              required={true}
            />

            <FormField
              label="Amount (INR)"
              name="amount"
              type="number"
              register={register}
              errors={errors}
              required={true}
              placeholder="Enter amount in INR"
              step="0.01"
              min="0"
              showCurrencySymbol={true}
            />

            <FormField
              label="Amount Period"
              name="amountPeriod"
              type="select"
              register={register}
              errors={errors}
              options={amountPeriodOptions}
              required={true}
            />
          </div>

          <div className="modal-actions">
            <button type="button" onClick={onCancel} className="cancel-btn" disabled={loading}>
              Cancel
            </button>
            <button type="submit" className="save-btn" disabled={loading}>
              {loading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default EditModal
