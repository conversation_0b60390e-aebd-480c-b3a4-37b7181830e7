.sales-table-container {
  width: 100%;
  max-width: 1400px;
  margin: 2rem auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.table-header h2 {
  color: #2d3748;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
}

.refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.table-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  align-items: center;
}

.search-box {
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: white;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-selects {
  display: flex;
  gap: 1rem;
}

.filter-select {
  padding: 0.875rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 0.9rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 150px;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.table-wrapper {
  overflow-x: auto;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.sales-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  font-size: 0.9rem;
}

.sales-table th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 0.75rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.85rem;
  white-space: nowrap;
  position: sticky;
  top: 0;
  z-index: 10;
}

.sales-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.sales-table th.sortable:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.sales-table td {
  padding: 1rem 0.75rem;
  border-bottom: 1px solid #e2e8f0;
  vertical-align: middle;
}

.sales-table tr:hover {
  background-color: #f7fafc;
}

.employee-name {
  font-weight: 600;
  color: #2d3748;
}

.project-name {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.amount {
  font-weight: 600;
  color: #38a169;
  text-align: right;
}

.vertical-badge,
.type-badge,
.period-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
  white-space: nowrap;
}

.vertical-badge.technology { background: #e6fffa; color: #234e52; }
.vertical-badge.healthcare { background: #fef5e7; color: #744210; }
.vertical-badge.finance { background: #e6f3ff; color: #1a365d; }
.vertical-badge.retail { background: #f0fff4; color: #22543d; }
.vertical-badge.manufacturing { background: #faf5ff; color: #553c9a; }
.vertical-badge.education { background: #fff5f5; color: #742a2a; }
.vertical-badge.government { background: #f7fafc; color: #2d3748; }
.vertical-badge.other { background: #fffaf0; color: #7c2d12; }

.type-badge.consulting { background: #e6f3ff; color: #1a365d; }
.type-badge.development { background: #e6fffa; color: #234e52; }
.type-badge.maintenance { background: #fef5e7; color: #744210; }
.type-badge.support { background: #f0fff4; color: #22543d; }
.type-badge.training { background: #faf5ff; color: #553c9a; }
.type-badge.implementation { background: #fff5f5; color: #742a2a; }
.type-badge.migration { background: #f7fafc; color: #2d3748; }
.type-badge.other { background: #fffaf0; color: #7c2d12; }

.period-badge.monthly { background: #e6f3ff; color: #1a365d; }
.period-badge.quarterly { background: #e6fffa; color: #234e52; }
.period-badge.half-yearly { background: #fef5e7; color: #744210; }
.period-badge.yearly { background: #f0fff4; color: #22543d; }

.actions-column {
  width: 120px;
  text-align: center;
}

.actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.edit-btn,
.delete-btn {
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.edit-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.edit-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(1.1);
}

.delete-btn {
  background: rgba(229, 62, 62, 0.1);
  color: #e53e3e;
}

.delete-btn:hover {
  background: rgba(229, 62, 62, 0.2);
  transform: scale(1.1);
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding: 1rem 0;
}

.pagination-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.pagination-info {
  color: #718096;
  font-weight: 500;
}

.no-data-message {
  text-align: center;
  padding: 3rem;
  color: #718096;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .sales-table-container {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .table-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .filter-selects {
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .sales-table-container {
    border-radius: 15px;
    padding: 1rem;
  }
  
  .table-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .sales-table {
    font-size: 0.8rem;
  }
  
  .sales-table th,
  .sales-table td {
    padding: 0.5rem 0.25rem;
  }
  
  .project-name {
    max-width: 120px;
  }
  
  .pagination {
    flex-direction: column;
    gap: 1rem;
  }
  
  .filter-selects {
    flex-direction: column;
  }
}
