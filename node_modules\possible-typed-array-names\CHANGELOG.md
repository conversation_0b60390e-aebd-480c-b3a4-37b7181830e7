# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.0](https://github.com/ljharb/possible-typed-array-names/compare/v1.0.0...v1.1.0) - 2025-02-06

### Commits

- [types] use shared tsconfig [`7d3057f`](https://github.com/ljharb/possible-typed-array-names/commit/7d3057f723d221c032951e618f45ad9044cae80d)
- [actions] split out node 10-20, and 20+ [`3cc8138`](https://github.com/ljharb/possible-typed-array-names/commit/3cc81385d6af59c096475080d76a4c78e6fef664)
- [actions] remove redundant finisher; use reusable workflows [`b46fe5d`](https://github.com/ljharb/possible-typed-array-names/commit/b46fe5d2d47054922f7be81acc0f3c2b7882ddab)
- [New] add `Float16Array` [`77df613`](https://github.com/ljharb/possible-typed-array-names/commit/77df61313d3491acfd23da0d4452673cca476644)
- [Dev Deps] update `@ljharb/eslint-config`, `@ljharb/tsconfig`, `@types/tape`, `auto-changelog`, `tape` [`85bba2e`](https://github.com/ljharb/possible-typed-array-names/commit/85bba2e359add86b19ef058d4a0560d369bf55a2)
- [Tests] tiny refactor [`b2ddd5a`](https://github.com/ljharb/possible-typed-array-names/commit/b2ddd5a9bc86b63631d9f2c17f21f0503492dbb3)
- [Dev Deps] update `@arethetypeswrong/cli`, `@ljharb/tsconfig`, `@types/tape` [`ed4447f`](https://github.com/ljharb/possible-typed-array-names/commit/ed4447f9ef1ad8657186282140a74ab474240d4e)
- [Tests] add attw; `postlint` [`b5b808c`](https://github.com/ljharb/possible-typed-array-names/commit/b5b808cebf0bc0bdb8636f4981cc8ffabb58bbbb)
- [Tests] replace `aud` with `npm audit` [`ce71c4e`](https://github.com/ljharb/possible-typed-array-names/commit/ce71c4e993e03b41034a4ca96fb8531dd8b8cc14)

## v1.0.0 - 2024-02-19

### Commits

- Initial implementation, tests, readme, types [`c279f55`](https://github.com/ljharb/possible-typed-array-names/commit/c279f550021896afa50c1169b3111618a96cf898)
- Initial commit [`0f22bf2`](https://github.com/ljharb/possible-typed-array-names/commit/0f22bf24d16fc8ea29483ed7ed378afb3758a4df)
- npm init [`25d6cff`](https://github.com/ljharb/possible-typed-array-names/commit/25d6cffe4091921e4e210704dabed37ae3d7b261)
- Only apps should have lockfiles [`a1bd592`](https://github.com/ljharb/possible-typed-array-names/commit/a1bd592fa037430d401b1d6d26cfea2c2d6789db)
