{"name": "promise-retry", "version": "2.0.1", "description": "Retries a function that returns a promise, leveraging the power of the retry module.", "main": "index.js", "scripts": {"test": "mocha --bail -t 10000"}, "bugs": {"url": "https://github.com/IndigoUnited/node-promise-retry/issues/"}, "repository": {"type": "git", "url": "git://github.com/IndigoUnited/node-promise-retry.git"}, "keywords": ["retry", "promise", "backoff", "repeat", "replay"], "author": "IndigoUnited <<EMAIL>> (http://indigounited.com)", "license": "MIT", "devDependencies": {"expect.js": "^0.3.1", "mocha": "^8.0.1", "sleep-promise": "^8.0.1"}, "dependencies": {"err-code": "^2.0.2", "retry": "^0.12.0"}, "engines": {"node": ">=10"}}