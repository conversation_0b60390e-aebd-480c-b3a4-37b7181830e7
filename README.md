# Finance Dashboard - Sales Projection

A modern React-based finance dashboard application for managing sales projections with MySQL database integration.

## Features

- **Sales Projection Form** with comprehensive validation
- **MySQL Database** integration for data persistence
- **RESTful API** backend with Express.js
- **Responsive Design** with modern UI/UX
- **Real-time Data** loading and submission
- **Form Validation** on both frontend and backend
- **Error Handling** and loading states

## Form Fields

- Employee ID
- Employee Name
- Vertical (Technology, Healthcare, Finance, etc.)
- Client
- Project
- Project Type (Consulting, Development, Maintenance, etc.)
- Start Date
- End Date
- Amount (with currency formatting)
- Amount Period (Monthly, Quarterly, Half Yearly, Yearly)

## Tech Stack

### Frontend
- React 18
- Vite
- React Hook Form
- Axios
- CSS3 with modern styling

### Backend
- Node.js
- Express.js
- SQLite3 (database)
- Joi (validation)
- CORS, Helmet (security)

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- No database installation required (uses SQLite)

## Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd finance-dashboard
```

### 2. Backend Setup

```bash
cd server
npm install
npm run dev
```

The backend server will start on `http://localhost:5000`

### 3. Frontend Setup

```bash
# From the root directory
npm install
npm run dev
```

The frontend will start on `http://localhost:3000`

## API Endpoints

### Sales Projections

- `GET /api/sales-projections` - Get all sales projections
- `GET /api/sales-projections/:id` - Get sales projection by ID
- `POST /api/sales-projections` - Create new sales projection
- `PUT /api/sales-projections/:id` - Update sales projection
- `DELETE /api/sales-projections/:id` - Delete sales projection

### Query Parameters

- `limit` - Number of records to return (default: 20, max: 100)
- `offset` - Number of records to skip (default: 0)
- `employeeId` - Filter by employee ID

## Database Schema

```sql
CREATE TABLE sales_projections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id VARCHAR(50) NOT NULL,
    employee_name VARCHAR(255) NOT NULL,
    vertical ENUM('technology', 'healthcare', 'finance', 'retail', 'manufacturing', 'education', 'government', 'other') NOT NULL,
    client VARCHAR(255) NOT NULL,
    project VARCHAR(255) NOT NULL,
    project_type ENUM('consulting', 'development', 'maintenance', 'support', 'training', 'implementation', 'migration', 'other') NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    amount DECIMAL(15, 2) NOT NULL,
    amount_period ENUM('monthly', 'quarterly', 'half-yearly', 'yearly') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Development

### Running in Development Mode

1. **Start Backend**:
   ```bash
   cd server
   npm run dev
   ```

2. **Start Frontend**:
   ```bash
   npm run dev
   ```

### Environment Variables

#### Frontend (.env)
```env
VITE_API_URL=http://localhost:5000/api
```

#### Backend (server/.env)
```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=finance_dashboard
PORT=5000
NODE_ENV=development
```

## Production Deployment

### Backend
```bash
cd server
npm start
```

### Frontend
```bash
npm run build
npm run preview
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Verify MySQL is running
   - Check database credentials in `server/.env`
   - Ensure database exists

2. **CORS Errors**
   - Verify frontend URL is allowed in backend CORS configuration
   - Check API URL in frontend `.env` file

3. **Port Already in Use**
   - Change PORT in `server/.env` for backend
   - Change port in `vite.config.js` for frontend

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the ISC License.
