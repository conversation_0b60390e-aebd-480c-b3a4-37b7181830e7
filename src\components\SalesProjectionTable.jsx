import React, { useState, useEffect } from 'react'
import { salesProjectionsAPI } from '../services/api'
import EditModal from './EditModal'
import DeleteConfirmModal from './DeleteConfirmModal'
import { formatCurrencyForDisplay } from '../utils/currency'
import '../styles/SalesProjectionTable.css'

const SalesProjectionTable = ({ onDataChange }) => {
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalRecords, setTotalRecords] = useState(0)
  const [recordsPerPage] = useState(10)
  const [sortField, setSortField] = useState('created_at')
  const [sortDirection, setSortDirection] = useState('desc')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterVertical, setFilterVertical] = useState('')
  const [filterProjectType, setFilterProjectType] = useState('')

  // Modal states
  const [editingRecord, setEditingRecord] = useState(null)
  const [deletingRecord, setDeletingRecord] = useState(null)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)

  const verticalOptions = [
    'technology', 'healthcare', 'finance', 'retail',
    'manufacturing', 'education', 'government', 'other'
  ]

  const projectTypeOptions = [
    'consulting', 'development', 'maintenance', 'support',
    'training', 'implementation', 'migration', 'other'
  ]

  useEffect(() => {
    loadData()
  }, [currentPage, sortField, sortDirection, searchTerm, filterVertical, filterProjectType])

  const loadData = async () => {
    try {
      setLoading(true)
      setError('')

      const params = {
        limit: recordsPerPage,
        offset: (currentPage - 1) * recordsPerPage
      }

      const response = await salesProjectionsAPI.getAll(params)

      if (response.success) {
        let filteredData = response.data

        // Apply client-side filtering and sorting since our API doesn't support it yet
        if (searchTerm) {
          filteredData = filteredData.filter(record =>
            record.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            record.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
            record.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
            record.project.toLowerCase().includes(searchTerm.toLowerCase())
          )
        }

        if (filterVertical) {
          filteredData = filteredData.filter(record => record.vertical === filterVertical)
        }

        if (filterProjectType) {
          filteredData = filteredData.filter(record => record.projectType === filterProjectType)
        }

        // Sort data
        filteredData.sort((a, b) => {
          let aValue = a[sortField]
          let bValue = b[sortField]

          if (sortField === 'amount') {
            aValue = parseFloat(aValue)
            bValue = parseFloat(bValue)
          } else if (sortField.includes('Date') || sortField.includes('_at')) {
            aValue = new Date(aValue)
            bValue = new Date(bValue)
          } else {
            aValue = aValue?.toString().toLowerCase() || ''
            bValue = bValue?.toString().toLowerCase() || ''
          }

          if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
          if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
          return 0
        })

        setData(filteredData)
        setTotalRecords(response.pagination?.total || filteredData.length)
      }
    } catch (err) {
      console.error('Failed to load data:', err)
      setError('Failed to load sales projections. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleEdit = (record) => {
    setEditingRecord(record)
    setShowEditModal(true)
  }

  const handleDelete = (record) => {
    setDeletingRecord(record)
    setShowDeleteModal(true)
  }

  const handleEditSave = async (updatedData) => {
    try {
      const response = await salesProjectionsAPI.update(editingRecord.id, updatedData)

      if (response.success) {
        setShowEditModal(false)
        setEditingRecord(null)
        await loadData()
        if (onDataChange) onDataChange()
      }
    } catch (err) {
      console.error('Failed to update record:', err)
      setError('Failed to update record. Please try again.')
    }
  }

  const handleDeleteConfirm = async () => {
    try {
      const response = await salesProjectionsAPI.delete(deletingRecord.id)

      if (response.success) {
        setShowDeleteModal(false)
        setDeletingRecord(null)
        await loadData()
        if (onDataChange) onDataChange()
      }
    } catch (err) {
      console.error('Failed to delete record:', err)
      setError('Failed to delete record. Please try again.')
    }
  }

  const formatCurrency = (amount) => {
    return formatCurrencyForDisplay(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getSortIcon = (field) => {
    if (sortField !== field) return '↕️'
    return sortDirection === 'asc' ? '↑' : '↓'
  }

  const totalPages = Math.ceil(totalRecords / recordsPerPage)

  return (
    <div className="sales-table-container">
      <div className="table-header">
        <h2>Sales Projections Database</h2>
        <button onClick={loadData} className="refresh-btn" disabled={loading}>
          {loading ? '🔄' : '↻'} Refresh
        </button>
      </div>

      {error && (
        <div className="error-message-banner">
          ❌ {error}
        </div>
      )}

      {/* Filters and Search */}
      <div className="table-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="Search by employee, client, or project..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-selects">
          <select
            value={filterVertical}
            onChange={(e) => setFilterVertical(e.target.value)}
            className="filter-select"
          >
            <option value="">All Verticals</option>
            {verticalOptions.map(option => (
              <option key={option} value={option}>
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </option>
            ))}
          </select>

          <select
            value={filterProjectType}
            onChange={(e) => setFilterProjectType(e.target.value)}
            className="filter-select"
          >
            <option value="">All Project Types</option>
            {projectTypeOptions.map(option => (
              <option key={option} value={option}>
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Table */}
      <div className="table-wrapper">
        {loading ? (
          <div className="loading-message">
            <div className="loading-spinner"></div>
            <p>Loading sales projections...</p>
          </div>
        ) : data.length === 0 ? (
          <div className="no-data-message">
            <p>No sales projections found.</p>
          </div>
        ) : (
          <table className="sales-table">
            <thead>
              <tr>
                <th onClick={() => handleSort('employeeId')} className="sortable">
                  Employee ID {getSortIcon('employeeId')}
                </th>
                <th onClick={() => handleSort('employeeName')} className="sortable">
                  Employee Name {getSortIcon('employeeName')}
                </th>
                <th onClick={() => handleSort('vertical')} className="sortable">
                  Vertical {getSortIcon('vertical')}
                </th>
                <th onClick={() => handleSort('client')} className="sortable">
                  Client {getSortIcon('client')}
                </th>
                <th onClick={() => handleSort('project')} className="sortable">
                  Project {getSortIcon('project')}
                </th>
                <th onClick={() => handleSort('projectType')} className="sortable">
                  Type {getSortIcon('projectType')}
                </th>
                <th onClick={() => handleSort('startDate')} className="sortable">
                  Start Date {getSortIcon('startDate')}
                </th>
                <th onClick={() => handleSort('endDate')} className="sortable">
                  End Date {getSortIcon('endDate')}
                </th>
                <th onClick={() => handleSort('amount')} className="sortable">
                  Amount {getSortIcon('amount')}
                </th>
                <th onClick={() => handleSort('amountPeriod')} className="sortable">
                  Period {getSortIcon('amountPeriod')}
                </th>
                <th onClick={() => handleSort('createdAt')} className="sortable">
                  Created {getSortIcon('createdAt')}
                </th>
                <th className="actions-column">Actions</th>
              </tr>
            </thead>
            <tbody>
              {data.map((record) => (
                <tr key={record.id}>
                  <td>{record.employeeId}</td>
                  <td className="employee-name">{record.employeeName}</td>
                  <td>
                    <span className={`vertical-badge ${record.vertical}`}>
                      {record.vertical}
                    </span>
                  </td>
                  <td>{record.client}</td>
                  <td className="project-name">{record.project}</td>
                  <td>
                    <span className={`type-badge ${record.projectType}`}>
                      {record.projectType}
                    </span>
                  </td>
                  <td>{formatDate(record.startDate)}</td>
                  <td>{formatDate(record.endDate)}</td>
                  <td className="amount">{formatCurrency(record.amount)}</td>
                  <td>
                    <span className={`period-badge ${record.amountPeriod}`}>
                      {record.amountPeriod}
                    </span>
                  </td>
                  <td>{formatDate(record.createdAt)}</td>
                  <td className="actions">
                    <button
                      onClick={() => handleEdit(record)}
                      className="edit-btn"
                      title="Edit Record"
                    >
                      ✏️
                    </button>
                    <button
                      onClick={() => handleDelete(record)}
                      className="delete-btn"
                      title="Delete Record"
                    >
                      🗑️
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="pagination-btn"
          >
            ← Previous
          </button>

          <span className="pagination-info">
            Page {currentPage} of {totalPages} ({totalRecords} total records)
          </span>

          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="pagination-btn"
          >
            Next →
          </button>
        </div>
      )}

      {/* Modals */}
      {showEditModal && (
        <EditModal
          record={editingRecord}
          onSave={handleEditSave}
          onCancel={() => {
            setShowEditModal(false)
            setEditingRecord(null)
          }}
        />
      )}

      {showDeleteModal && (
        <DeleteConfirmModal
          record={deletingRecord}
          onConfirm={handleDeleteConfirm}
          onCancel={() => {
            setShowDeleteModal(false)
            setDeletingRecord(null)
          }}
        />
      )}
    </div>
  )
}

export default SalesProjectionTable
