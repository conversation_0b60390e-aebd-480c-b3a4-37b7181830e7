import React, { useState } from 'react'
import { formatCurrencyForDisplay } from '../utils/currency'
import '../styles/Modal.css'

const DeleteConfirmModal = ({ record, onConfirm, onCancel }) => {
  const [loading, setLoading] = useState(false)

  const handleConfirm = async () => {
    try {
      setLoading(true)
      await onConfirm()
    } catch (err) {
      console.error('Delete failed:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onCancel()
    }
  }

  const formatCurrency = (amount) => {
    return formatCurrencyForDisplay(amount)
  }

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className="modal-content delete-modal">
        <div className="modal-header">
          <h2>⚠️ Confirm Delete</h2>
          <button onClick={onCancel} className="modal-close-btn">×</button>
        </div>

        <div className="delete-modal-body">
          <p className="delete-warning">
            Are you sure you want to delete this sales projection? This action cannot be undone.
          </p>

          <div className="record-summary">
            <h3>Record Details:</h3>
            <div className="record-details">
              <div className="detail-row">
                <span className="detail-label">Employee:</span>
                <span className="detail-value">{record.employeeName} ({record.employeeId})</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Project:</span>
                <span className="detail-value">{record.project}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Client:</span>
                <span className="detail-value">{record.client}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Amount:</span>
                <span className="detail-value">
                  {formatCurrency(record.amount)} ({record.amountPeriod})
                </span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Vertical:</span>
                <span className="detail-value">{record.vertical}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Project Type:</span>
                <span className="detail-value">{record.projectType}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="modal-actions">
          <button
            type="button"
            onClick={onCancel}
            className="cancel-btn"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleConfirm}
            className="delete-confirm-btn"
            disabled={loading}
          >
            {loading ? 'Deleting...' : 'Delete Record'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default DeleteConfirmModal
