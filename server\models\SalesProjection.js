import { executeQuery } from '../config/database.js';

class SalesProjection {
  constructor(data) {
    this.employee_id = data.employee_id || data.employeeId;
    this.employee_name = data.employee_name || data.employeeName;
    this.vertical = data.vertical;
    this.client = data.client;
    this.project = data.project;
    this.project_type = data.project_type || data.projectType;
    this.start_date = data.start_date || data.startDate;
    this.end_date = data.end_date || data.endDate;
    this.amount = data.amount;
    this.amount_period = data.amount_period || data.amountPeriod;
  }

  // Create a new sales projection
  static async create(projectionData) {
    const projection = new SalesProjection(projectionData);
    
    const query = `
      INSERT INTO sales_projections (
        employee_id, employee_name, vertical, client, project, 
        project_type, start_date, end_date, amount, amount_period
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      projection.employee_id,
      projection.employee_name,
      projection.vertical,
      projection.client,
      projection.project,
      projection.project_type,
      projection.start_date,
      projection.end_date,
      projection.amount,
      projection.amount_period
    ];

    const result = await executeQuery(query, params);
    
    if (result.success) {
      return { success: true, id: result.data.insertId, data: projection };
    }
    
    return result;
  }

  // Get all sales projections
  static async findAll(limit = 100, offset = 0) {
    const query = `
      SELECT 
        id,
        employee_id as employeeId,
        employee_name as employeeName,
        vertical,
        client,
        project,
        project_type as projectType,
        start_date as startDate,
        end_date as endDate,
        amount,
        amount_period as amountPeriod,
        created_at as createdAt,
        updated_at as updatedAt
      FROM sales_projections 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    
    return await executeQuery(query, [limit, offset]);
  }

  // Get sales projection by ID
  static async findById(id) {
    const query = `
      SELECT 
        id,
        employee_id as employeeId,
        employee_name as employeeName,
        vertical,
        client,
        project,
        project_type as projectType,
        start_date as startDate,
        end_date as endDate,
        amount,
        amount_period as amountPeriod,
        created_at as createdAt,
        updated_at as updatedAt
      FROM sales_projections 
      WHERE id = ?
    `;
    
    const result = await executeQuery(query, [id]);
    
    if (result.success && result.data.length > 0) {
      return { success: true, data: result.data[0] };
    }
    
    return { success: false, error: 'Sales projection not found' };
  }

  // Update sales projection
  static async update(id, projectionData) {
    const projection = new SalesProjection(projectionData);
    
    const query = `
      UPDATE sales_projections SET
        employee_id = ?,
        employee_name = ?,
        vertical = ?,
        client = ?,
        project = ?,
        project_type = ?,
        start_date = ?,
        end_date = ?,
        amount = ?,
        amount_period = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    const params = [
      projection.employee_id,
      projection.employee_name,
      projection.vertical,
      projection.client,
      projection.project,
      projection.project_type,
      projection.start_date,
      projection.end_date,
      projection.amount,
      projection.amount_period,
      id
    ];

    const result = await executeQuery(query, params);
    
    if (result.success && result.data.affectedRows > 0) {
      return { success: true, data: projection };
    }
    
    return { success: false, error: 'Sales projection not found or no changes made' };
  }

  // Delete sales projection
  static async delete(id) {
    const query = 'DELETE FROM sales_projections WHERE id = ?';
    const result = await executeQuery(query, [id]);
    
    if (result.success && result.data.affectedRows > 0) {
      return { success: true, message: 'Sales projection deleted successfully' };
    }
    
    return { success: false, error: 'Sales projection not found' };
  }

  // Get projections by employee ID
  static async findByEmployeeId(employeeId) {
    const query = `
      SELECT 
        id,
        employee_id as employeeId,
        employee_name as employeeName,
        vertical,
        client,
        project,
        project_type as projectType,
        start_date as startDate,
        end_date as endDate,
        amount,
        amount_period as amountPeriod,
        created_at as createdAt,
        updated_at as updatedAt
      FROM sales_projections 
      WHERE employee_id = ?
      ORDER BY created_at DESC
    `;
    
    return await executeQuery(query, [employeeId]);
  }

  // Get total count of projections
  static async getCount() {
    const query = 'SELECT COUNT(*) as total FROM sales_projections';
    const result = await executeQuery(query);
    
    if (result.success) {
      return { success: true, total: result.data[0].total };
    }
    
    return result;
  }
}

export default SalesProjection;
