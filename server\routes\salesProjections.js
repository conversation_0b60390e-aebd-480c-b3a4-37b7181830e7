import express from 'express';
import SalesProjection from '../models/SalesProjection.js';
import { validateSalesProjection, validateId, validateListQuery } from '../middleware/validation.js';

const router = express.Router();

// GET /api/sales-projections - Get all sales projections
router.get('/', validateListQuery, async (req, res) => {
  try {
    const { limit, offset, employeeId } = req.validatedQuery;
    
    let result;
    if (employeeId) {
      result = await SalesProjection.findByEmployeeId(employeeId);
    } else {
      result = await SalesProjection.findAll(limit, offset);
    }

    if (result.success) {
      // Get total count for pagination
      const countResult = await SalesProjection.getCount();
      const total = countResult.success ? countResult.total : 0;

      res.json({
        success: true,
        data: result.data,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to fetch sales projections',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error fetching sales projections:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// GET /api/sales-projections/:id - Get sales projection by ID
router.get('/:id', validateId, async (req, res) => {
  try {
    const result = await SalesProjection.findById(req.validatedId);

    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(404).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Error fetching sales projection:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// POST /api/sales-projections - Create new sales projection
router.post('/', validateSalesProjection, async (req, res) => {
  try {
    const result = await SalesProjection.create(req.validatedData);

    if (result.success) {
      res.status(201).json({
        success: true,
        message: 'Sales projection created successfully',
        data: {
          id: result.id,
          ...result.data
        }
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to create sales projection',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error creating sales projection:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// PUT /api/sales-projections/:id - Update sales projection
router.put('/:id', validateId, validateSalesProjection, async (req, res) => {
  try {
    const result = await SalesProjection.update(req.validatedId, req.validatedData);

    if (result.success) {
      res.json({
        success: true,
        message: 'Sales projection updated successfully',
        data: result.data
      });
    } else {
      res.status(404).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Error updating sales projection:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// DELETE /api/sales-projections/:id - Delete sales projection
router.delete('/:id', validateId, async (req, res) => {
  try {
    const result = await SalesProjection.delete(req.validatedId);

    if (result.success) {
      res.json({
        success: true,
        message: result.message
      });
    } else {
      res.status(404).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Error deleting sales projection:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

export default router;
