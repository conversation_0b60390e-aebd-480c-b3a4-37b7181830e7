import React, { useState } from 'react';
import '../styles/Settings.css';

const Settings = () => {
  const [activeSection, setActiveSection] = useState('account');
  
  // Sample user data
  const userData = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Financial Analyst',
    department: 'Finance',
    joinDate: '2023-05-15',
    lastLogin: '2025-05-27T09:30:00'
  };
  
  // Sample notification settings
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    newProjections: true,
    reportGeneration: true,
    systemUpdates: false,
    weeklyDigest: true
  });
  
  const handleNotificationChange = (setting) => {
    setNotificationSettings({
      ...notificationSettings,
      [setting]: !notificationSettings[setting]
    });
  };

  return (
    <div className="settings-container">
      <div className="settings-header">
        <h2>Settings</h2>
        <p>Manage your account and application preferences</p>
      </div>

      <div className="settings-content">
        <div className="settings-sidebar">
          <button 
            className={`sidebar-item ${activeSection === 'account' ? 'active' : ''}`}
            onClick={() => setActiveSection('account')}
          >
            <span className="sidebar-icon">👤</span>
            <span>Account Settings</span>
          </button>
          <button 
            className={`sidebar-item ${activeSection === 'notifications' ? 'active' : ''}`}
            onClick={() => setActiveSection('notifications')}
          >
            <span className="sidebar-icon">🔔</span>
            <span>Notifications</span>
          </button>
          <button 
            className={`sidebar-item ${activeSection === 'appearance' ? 'active' : ''}`}
            onClick={() => setActiveSection('appearance')}
          >
            <span className="sidebar-icon">🎨</span>
            <span>Appearance</span>
          </button>
          <button 
            className={`sidebar-item ${activeSection === 'data' ? 'active' : ''}`}
            onClick={() => setActiveSection('data')}
          >
            <span className="sidebar-icon">💾</span>
            <span>Data Management</span>
          </button>
          <button 
            className={`sidebar-item ${activeSection === 'security' ? 'active' : ''}`}
            onClick={() => setActiveSection('security')}
          >
            <span className="sidebar-icon">🔒</span>
            <span>Security</span>
          </button>
          <button 
            className={`sidebar-item ${activeSection === 'api' ? 'active' : ''}`}
            onClick={() => setActiveSection('api')}
          >
            <span className="sidebar-icon">🔌</span>
            <span>API Access</span>
          </button>
        </div>

        <div className="settings-main">
          {activeSection === 'account' && (
            <div className="settings-section">
              <h3>Account Settings</h3>
              
              <div className="profile-card">
                <div className="profile-header">
                  <div className="profile-avatar">
                    <span>{userData.name.split(' ').map(n => n[0]).join('')}</span>
                  </div>
                  <div className="profile-details">
                    <h4>{userData.name}</h4>
                    <p>{userData.role} • {userData.department}</p>
                    <p className="profile-meta">
                      Member since {new Date(userData.joinDate).toLocaleDateString()}
                    </p>
                  </div>
                  <button className="edit-profile-btn">Edit Profile</button>
                </div>
              </div>
              
              <div className="settings-form">
                <div className="form-group">
                  <label>Full Name</label>
                  <input type="text" value={userData.name} readOnly />
                </div>
                <div className="form-group">
                  <label>Email Address</label>
                  <input type="email" value={userData.email} readOnly />
                </div>
                <div className="form-row">
                  <div className="form-group">
                    <label>Role</label>
                    <input type="text" value={userData.role} readOnly />
                  </div>
                  <div className="form-group">
                    <label>Department</label>
                    <input type="text" value={userData.department} readOnly />
                  </div>
                </div>
                <div className="form-group">
                  <label>Time Zone</label>
                  <select defaultValue="America/New_York">
                    <option value="America/New_York">Eastern Time (ET)</option>
                    <option value="America/Chicago">Central Time (CT)</option>
                    <option value="America/Denver">Mountain Time (MT)</option>
                    <option value="America/Los_Angeles">Pacific Time (PT)</option>
                    <option value="Europe/London">Greenwich Mean Time (GMT)</option>
                  </select>
                </div>
                <div className="form-actions">
                  <button className="save-btn">Save Changes</button>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'notifications' && (
            <div className="settings-section">
              <h3>Notification Settings</h3>
              <p className="section-description">
                Configure how and when you receive notifications from the Finance Dashboard.
              </p>
              
              <div className="notification-settings">
                <div className="notification-group">
                  <div className="notification-header">
                    <h4>Email Notifications</h4>
                    <label className="toggle-switch">
                      <input 
                        type="checkbox" 
                        checked={notificationSettings.emailNotifications}
                        onChange={() => handleNotificationChange('emailNotifications')}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>
                  <p className="notification-description">
                    Receive notifications via email
                  </p>
                </div>
                
                <div className="notification-options">
                  <div className="notification-option">
                    <div className="option-label">
                      <span className="option-icon">📊</span>
                      <span>New Projections</span>
                    </div>
                    <label className="toggle-switch">
                      <input 
                        type="checkbox" 
                        checked={notificationSettings.newProjections}
                        onChange={() => handleNotificationChange('newProjections')}
                        disabled={!notificationSettings.emailNotifications}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>
                  <div className="notification-option">
                    <div className="option-label">
                      <span className="option-icon">📈</span>
                      <span>Report Generation</span>
                    </div>
                    <label className="toggle-switch">
                      <input 
                        type="checkbox" 
                        checked={notificationSettings.reportGeneration}
                        onChange={() => handleNotificationChange('reportGeneration')}
                        disabled={!notificationSettings.emailNotifications}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>
                  <div className="notification-option">
                    <div className="option-label">
                      <span className="option-icon">🔄</span>
                      <span>System Updates</span>
                    </div>
                    <label className="toggle-switch">
                      <input 
                        type="checkbox" 
                        checked={notificationSettings.systemUpdates}
                        onChange={() => handleNotificationChange('systemUpdates')}
                        disabled={!notificationSettings.emailNotifications}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>
                  <div className="notification-option">
                    <div className="option-label">
                      <span className="option-icon">📅</span>
                      <span>Weekly Digest</span>
                    </div>
                    <label className="toggle-switch">
                      <input 
                        type="checkbox" 
                        checked={notificationSettings.weeklyDigest}
                        onChange={() => handleNotificationChange('weeklyDigest')}
                        disabled={!notificationSettings.emailNotifications}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>
                </div>
                
                <div className="form-actions">
                  <button className="save-btn">Save Notification Settings</button>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'appearance' && (
            <div className="settings-section">
              <h3>Appearance Settings</h3>
              <p className="section-description">
                Customize the look and feel of your Finance Dashboard.
              </p>
              
              <div className="appearance-settings">
                <div className="theme-selector">
                  <h4>Theme</h4>
                  <div className="theme-options">
                    <div className="theme-option active">
                      <div className="theme-preview light-theme"></div>
                      <span>Light</span>
                    </div>
                    <div className="theme-option">
                      <div className="theme-preview dark-theme"></div>
                      <span>Dark</span>
                    </div>
                    <div className="theme-option">
                      <div className="theme-preview system-theme"></div>
                      <span>System</span>
                    </div>
                  </div>
                </div>
                
                <div className="color-selector">
                  <h4>Accent Color</h4>
                  <div className="color-options">
                    <div className="color-option active" style={{ backgroundColor: '#4361ee' }}></div>
                    <div className="color-option" style={{ backgroundColor: '#3a0ca3' }}></div>
                    <div className="color-option" style={{ backgroundColor: '#f72585' }}></div>
                    <div className="color-option" style={{ backgroundColor: '#4cc9f0' }}></div>
                    <div className="color-option" style={{ backgroundColor: '#4caf50' }}></div>
                    <div className="color-option" style={{ backgroundColor: '#ff9800' }}></div>
                  </div>
                </div>
                
                <div className="density-selector">
                  <h4>Display Density</h4>
                  <div className="density-options">
                    <button className="density-option">Compact</button>
                    <button className="density-option active">Default</button>
                    <button className="density-option">Comfortable</button>
                  </div>
                </div>
                
                <div className="form-actions">
                  <button className="save-btn">Save Appearance Settings</button>
                </div>
              </div>
            </div>
          )}

          {(activeSection === 'data' || activeSection === 'security' || activeSection === 'api') && (
            <div className="settings-section">
              <h3>{activeSection === 'data' ? 'Data Management' : 
                   activeSection === 'security' ? 'Security Settings' : 'API Access'}</h3>
              <div className="placeholder-content">
                <span className="placeholder-icon">
                  {activeSection === 'data' ? '💾' : 
                   activeSection === 'security' ? '🔒' : '🔌'}
                </span>
                <h4>Coming Soon</h4>
                <p>This feature is currently under development and will be available soon.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;