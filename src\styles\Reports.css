/* Reports Styles */
.reports-container {
  width: 100%;
}

.reports-header {
  margin-bottom: 2rem;
}

.reports-header h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.reports-header p {
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Reports Content Layout */
.reports-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 1.5rem;
}

/* Sidebar Styles */
.reports-sidebar {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.sidebar-section {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-section:last-child {
  border-bottom: none;
}

.sidebar-section h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.25rem;
}

/* Template List */
.template-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.template-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: var(--border-radius);
  background-color: transparent;
  border: 1px solid transparent;
  text-align: left;
  transition: var(--transition);
  cursor: pointer;
  width: 100%;
}

.template-item:hover {
  background-color: var(--bg-subtle);
  border-color: var(--border-color);
}

.template-item.active {
  background-color: rgba(0, 86, 179, 0.05);
  border-color: var(--primary-color);
}

.template-icon {
  font-size: 1.25rem;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
}

.template-item.active .template-icon {
  background-color: rgba(0, 86, 179, 0.1);
  color: var(--primary-color);
}

.template-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.template-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.template-desc {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* Saved Reports */
.saved-reports-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.saved-report-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: var(--border-radius);
  background-color: var(--bg-white);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.saved-report-item:hover {
  background-color: var(--bg-subtle);
  border-color: var(--primary-light);
}

.report-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 86, 179, 0.1);
  border-radius: var(--border-radius);
  font-size: 1.25rem;
  color: var(--primary-color);
}

.report-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.report-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.report-meta {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.report-actions {
  display: flex;
  gap: 0.5rem;
}

.action-icon {
  background: none;
  border: none;
  font-size: 1rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
}

.action-icon:hover {
  color: var(--primary-color);
}

.view-all-reports-btn {
  width: 100%;
  padding: 0.75rem;
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--primary-color);
  font-weight: 500;
  transition: var(--transition);
}

.view-all-reports-btn:hover {
  background-color: var(--bg-subtle);
  border-color: var(--primary-light);
}

/* Report Builder */
.report-builder {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.builder-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.builder-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.builder-actions {
  display: flex;
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.25rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition);
}

.secondary-btn {
  background-color: var(--bg-white);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.secondary-btn:hover {
  background-color: var(--bg-subtle);
  color: var(--primary-color);
  border-color: var(--primary-light);
}

.primary-btn {
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
  color: white;
}

.primary-btn:hover {
  background-color: var(--primary-dark);
  box-shadow: var(--box-shadow);
}

/* Builder Content */
.builder-content {
  padding: 1.5rem;
}

.builder-section {
  margin-bottom: 2rem;
}

.builder-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.25rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

/* Parameter Grid */
.parameter-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.25rem;
}

.parameter-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.parameter-field label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.parameter-field input,
.parameter-field select {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  color: var(--text-primary);
  background-color: var(--bg-white);
  transition: var(--transition);
}

.parameter-field input:hover,
.parameter-field select:hover {
  border-color: var(--primary-light);
}

.parameter-field input:focus,
.parameter-field select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.1);
}

/* Component List */
.component-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.component-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.component-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
  cursor: pointer;
}

.component-item label {
  font-size: 0.9rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition);
}

.component-item label:hover {
  color: var(--primary-color);
}

/* Report Preview */
.report-preview {
  background-color: var(--bg-subtle);
  border: 1px dashed var(--border-color);
  border-radius: var(--border-radius);
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-placeholder {
  text-align: center;
  padding: 2rem;
}

.preview-icon {
  font-size: 2.5rem;
  display: block;
  margin-bottom: 1rem;
  color: var(--primary-color);
  opacity: 0.6;
}

.preview-placeholder p {
  color: var(--text-secondary);
  max-width: 300px;
  margin: 0 auto;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .reports-content {
    grid-template-columns: 1fr;
  }
  
  .reports-sidebar {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0;
  }
  
  .sidebar-section {
    border-bottom: none;
  }
  
  .sidebar-section:first-child {
    border-right: 1px solid var(--border-color);
  }
}

@media (max-width: 768px) {
  .reports-sidebar {
    grid-template-columns: 1fr;
  }
  
  .sidebar-section:first-child {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .builder-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .builder-actions {
    width: 100%;
  }
  
  .action-btn {
    flex: 1;
    justify-content: center;
  }
  
  .parameter-grid {
    grid-template-columns: 1fr;
  }
  
  .component-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .component-list {
    grid-template-columns: 1fr;
  }
}