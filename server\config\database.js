import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';

dotenv.config();

const dbPath = process.env.DB_PATH || './database/finance_dashboard.db';

// Ensure database directory exists
const dbDir = path.dirname(dbPath);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Database connection
let db = null;

// Initialize database connection
export const initDatabase = async () => {
  if (!db) {
    try {
      db = await open({
        filename: dbPath,
        driver: sqlite3.Database
      });

      // Enable foreign keys
      await db.exec('PRAGMA foreign_keys = ON');

      console.log('✅ SQLite database connected successfully');
      return true;
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }
  }
  return true;
};

// Test database connection
export const testConnection = async () => {
  try {
    await initDatabase();
    if (db) {
      await db.get('SELECT 1');
      console.log('✅ Database connection test successful');
      return true;
    }
    return false;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
};

// Execute query with error handling
export const executeQuery = async (query, params = []) => {
  try {
    await initDatabase();

    if (query.trim().toUpperCase().startsWith('SELECT')) {
      const results = await db.all(query, params);
      return { success: true, data: results };
    } else if (query.trim().toUpperCase().startsWith('INSERT')) {
      const result = await db.run(query, params);
      return { success: true, data: { insertId: result.lastID, affectedRows: result.changes } };
    } else {
      const result = await db.run(query, params);
      return { success: true, data: { affectedRows: result.changes } };
    }
  } catch (error) {
    console.error('Database query error:', error);
    return { success: false, error: error.message };
  }
};

// Get database instance
export const getDatabase = async () => {
  await initDatabase();
  return db;
};

// Initialize database schema
export const initSchema = async () => {
  try {
    await initDatabase();

    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS sales_projections (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id TEXT NOT NULL,
        employee_name TEXT NOT NULL,
        vertical TEXT NOT NULL CHECK (vertical IN ('technology', 'healthcare', 'finance', 'retail', 'manufacturing', 'education', 'government', 'other')),
        client TEXT NOT NULL,
        project TEXT NOT NULL,
        project_type TEXT NOT NULL CHECK (project_type IN ('consulting', 'development', 'maintenance', 'support', 'training', 'implementation', 'migration', 'other')),
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        amount DECIMAL(15, 2) NOT NULL,
        amount_period TEXT NOT NULL CHECK (amount_period IN ('monthly', 'quarterly', 'half-yearly', 'yearly')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    await db.exec(createTableQuery);

    // Create indexes
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_employee_id ON sales_projections(employee_id)',
      'CREATE INDEX IF NOT EXISTS idx_vertical ON sales_projections(vertical)',
      'CREATE INDEX IF NOT EXISTS idx_client ON sales_projections(client)',
      'CREATE INDEX IF NOT EXISTS idx_project_type ON sales_projections(project_type)',
      'CREATE INDEX IF NOT EXISTS idx_start_date ON sales_projections(start_date)',
      'CREATE INDEX IF NOT EXISTS idx_created_at ON sales_projections(created_at)'
    ];

    for (const indexQuery of indexes) {
      await db.exec(indexQuery);
    }

    // Database schema created successfully - no sample data inserted

    console.log('✅ Database schema initialized successfully');
    return true;
  } catch (error) {
    console.error('❌ Failed to initialize database schema:', error);
    return false;
  }
};

export default db;
