@echo off
echo ========================================
echo Finance Dashboard - Stop All Servers
echo ========================================
echo.

echo Stopping all Node.js processes...
echo.

echo Killing frontend development servers (port 3000-3010)...
for /L %%i in (3000,1,3010) do (
    netstat -ano | findstr :%%i >nul 2>&1
    if not errorlevel 1 (
        echo Found process on port %%i, attempting to stop...
        for /f "tokens=5" %%a in ('netstat -ano ^| findstr :%%i') do (
            taskkill /PID %%a /F >nul 2>&1
        )
    )
)

echo.
echo Killing backend server (port 5001)...
netstat -ano | findstr :5001 >nul 2>&1
if not errorlevel 1 (
    echo Found process on port 5001, attempting to stop...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5001') do (
        taskkill /PID %%a /F >nul 2>&1
    )
) else (
    echo No process found on port 5001
)

echo.
echo Killing any remaining Node.js processes...
taskkill /IM node.exe /F >nul 2>&1
taskkill /IM npm.exe /F >nul 2>&1

echo.
echo ========================================
echo 🛑 All servers stopped!
echo ========================================
echo.
echo All Finance Dashboard servers have been stopped.
echo You can now safely close this window.
echo.
pause
