# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v2.0.2](https://github.com/inspect-js/is-weakmap/compare/v2.0.1...v2.0.2) - 2024-03-08

### Commits

- [actions] reuse common workflows [`0af1292`](https://github.com/inspect-js/is-weakmap/commit/0af1292f196cb1192acd4cea1b2eef00267a7e35)
- [Tests] migrate tests to Github Actions [`f35679b`](https://github.com/inspect-js/is-weakmap/commit/f35679bad4f11be0b6e46aae85cbcab14d8a107e)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`cd2f0fa`](https://github.com/inspect-js/is-weakmap/commit/cd2f0fa90293650cfdad8b933f976445071b34c9)
- add types [`6e28e6a`](https://github.com/inspect-js/is-weakmap/commit/6e28e6ab76e9227aefdf39049f261873e5b391b6)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `es6-shim`, `object-inspect`, `tape` [`95815ce`](https://github.com/inspect-js/is-weakmap/commit/95815ce0e51ce37fa799314828ad87e73a08b37d)
- [readme] update URLs [`2c4eb0b`](https://github.com/inspect-js/is-weakmap/commit/2c4eb0bec14e5015da378a8b6547b2740d7bcff3)
- [Tests] run `nyc` on all tests; use `tape` runner [`0a45b37`](https://github.com/inspect-js/is-weakmap/commit/0a45b37a18afc28476e39f9fdf0fb42c78a1ff4c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `safe-publish-latest`, `tape` [`ffe9459`](https://github.com/inspect-js/is-weakmap/commit/ffe945997accbae9583f76bd695559b0ba50274d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `es5-shim`, `object-inspect`, `tape` [`c3c2f11`](https://github.com/inspect-js/is-weakmap/commit/c3c2f1176078dedf5138b7928b539c3a26e72685)
- [actions] remove redundant finisher [`afa4018`](https://github.com/inspect-js/is-weakmap/commit/afa4018a6759b7c603c7c104a8035295ea6eb2eb)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `core-js`, `es5-shim`, `object-inspect`, `safe-publish-latest`, `tape` [`3f22fc1`](https://github.com/inspect-js/is-weakmap/commit/3f22fc126fc442c020858266af7c46a741638e62)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `es6-shim`, `npmignore`, `object-inspect`, `tape` [`84ba754`](https://github.com/inspect-js/is-weakmap/commit/84ba7549ce1ef2f01d4aae6436b821c52fe49b22)
- [actions] update rebase action to use reusable workflow [`def85cb`](https://github.com/inspect-js/is-weakmap/commit/def85cb49bff74528f5e13610ca8b823fbb02aae)
- [actions] update codecov uploader [`659031e`](https://github.com/inspect-js/is-weakmap/commit/659031e04714fe5a0225501a6f2589d8fae0d046)
- [actions] add "Allow Edits" workflow [`a916e89`](https://github.com/inspect-js/is-weakmap/commit/a916e8952f0ac5091704ce8fd3e7dfd3c265c2e1)
- [readme] remove travis badge [`01d0c7d`](https://github.com/inspect-js/is-weakmap/commit/01d0c7d19354ac73968be67290b81becfff428cf)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`ad90d06`](https://github.com/inspect-js/is-weakmap/commit/ad90d063213d305a6c8226f5a45d8991f74eb10a)
- [meta] use `npmignore` to autogenerate an npmignore file [`8b0b44f`](https://github.com/inspect-js/is-weakmap/commit/8b0b44f77be97b3c6c09c1ff7464a23774dd9ee6)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `es5-shim`, `tape` [`9d8a683`](https://github.com/inspect-js/is-weakmap/commit/9d8a683d23d755fe6d55364d0c7ebee65af572de)
- [Tests] add `core-js` tests [`8ba8d2b`](https://github.com/inspect-js/is-weakmap/commit/8ba8d2b07b126b5c87a2ba837984337cde9e9ab9)
- [readme] add actions and codecov badges [`49769ce`](https://github.com/inspect-js/is-weakmap/commit/49769ce6e57edb6a1461b4c786ca3ef7609a7972)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`377dfac`](https://github.com/inspect-js/is-weakmap/commit/377dfac3dfc0ad193a45d1d2bd13c1e5f00194f8)
- [actions] switch Automatic Rease workflow to `pull_request_target` event [`00f8c17`](https://github.com/inspect-js/is-weakmap/commit/00f8c172d18c929dc6a5b43fe1854a7742200432)
- [Dev Deps] update `es5-shim`, `tape` [`3a82ee8`](https://github.com/inspect-js/is-weakmap/commit/3a82ee8aff1d5f3c450edac1d4c96d824918d42c)
- [meta] add missing `engines.node` [`3671b4f`](https://github.com/inspect-js/is-weakmap/commit/3671b4f0b2f07d03c447d2297807c17bbbaf313e)
- [meta] use `prepublishOnly` script for npm 7+ [`2a5d5ea`](https://github.com/inspect-js/is-weakmap/commit/2a5d5ea541d3a25667a549fbdc95f7969791c7ba)
- [Dev Deps] update `auto-changelog`; add `aud` [`595583b`](https://github.com/inspect-js/is-weakmap/commit/595583b5a1383ab3768038f8532d87e85d7fa1f2)
- [readme] remove dead badges [`4d6cb2c`](https://github.com/inspect-js/is-weakmap/commit/4d6cb2cc35abd931e52abc4bc915eafc9d6f74b9)
- [Tests] run all tests on `npm test` [`9da2487`](https://github.com/inspect-js/is-weakmap/commit/9da24874b64807159d1f80add60804ccf32ac84a)
- [Tests] only audit prod deps [`2f9281a`](https://github.com/inspect-js/is-weakmap/commit/2f9281aa6fc26b15353515a089f2a312cf48b081)
- [meta] add `sideEffects` flag [`b9c8797`](https://github.com/inspect-js/is-weakmap/commit/b9c87974922c16768f1cf11bf450ca7d5dc55ef9)

## [v2.0.1](https://github.com/inspect-js/is-weakmap/compare/v2.0.0...v2.0.1) - 2019-12-17

### Fixed

- [Refactor] avoid top-level return, because babel and webpack are broken [`#79`](https://github.com/inspect-js/node-deep-equal/issues/79) [`#78`](https://github.com/inspect-js/node-deep-equal/issues/78) [`#7`](https://github.com/es-shims/Promise.allSettled/issues/7) [`#12`](https://github.com/airbnb/js-shims/issues/12)

### Commits

- [actions] add automatic rebasing / merge commit blocking [`4fa3010`](https://github.com/inspect-js/is-weakmap/commit/4fa301026787589c5a061072fda64b11d65bda18)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`44bafb6`](https://github.com/inspect-js/is-weakmap/commit/44bafb65829d687fcf8205d0c451cc407d96463c)

## [v2.0.0](https://github.com/inspect-js/is-weakmap/compare/v1.0.1...v2.0.0) - 2019-11-12

### Commits

- Initial commit [`6e9bd4a`](https://github.com/inspect-js/is-weakmap/commit/6e9bd4a0d61deadbf40d9875033ebdf430924236)
- Tests [`61985dd`](https://github.com/inspect-js/is-weakmap/commit/61985ddf042687f2c6d8c884200f576e9cc0f29d)
- implementation [`67b468d`](https://github.com/inspect-js/is-weakmap/commit/67b468db3390671c14ad656d3489e7422151b2bf)
- readme [`b0ed982`](https://github.com/inspect-js/is-weakmap/commit/b0ed9826547c25cfe2ed0c6e1258d407cb76e6f4)
- npm init [`54a1f81`](https://github.com/inspect-js/is-weakmap/commit/54a1f815702bde057a83d6bc0d69816c3644d698)
- [meta] add `funding` field; create `FUNDING.yml` [`74579bc`](https://github.com/inspect-js/is-weakmap/commit/74579bc96345f9d15392b384d765204f398fb3c3)
- [meta] add `safe-publish-latest`, `auto-changelog` [`9495b13`](https://github.com/inspect-js/is-weakmap/commit/9495b13cea989c344fbb5747f1471feb24f35959)
- [Tests] add `npm run lint` [`4d4657d`](https://github.com/inspect-js/is-weakmap/commit/4d4657d396ec9e2b6625b937fcc8794bd5583fd3)
- [Tests] use shared travis-ci configs [`1db25d5`](https://github.com/inspect-js/is-weakmap/commit/1db25d515fa042c39828c3cbfac65667722a679b)
- Only apps should have lockfiles [`f6b0152`](https://github.com/inspect-js/is-weakmap/commit/f6b015293a4702c9cb7672a364d725ae6cc8bca8)
- [Tests] add `npx aud` in `posttest` [`35dce96`](https://github.com/inspect-js/is-weakmap/commit/35dce964f73ef11237d12b0759468526e0e628a2)

## [v1.0.1](https://github.com/inspect-js/is-weakmap/compare/v1.0.0...v1.0.1) - 2015-08-31

### Commits

- Add XO [`180fb3e`](https://github.com/inspect-js/is-weakmap/commit/180fb3edf1ab1a2a449bdf5fae5911115d804f44)
- tweaks [`b0d7b30`](https://github.com/inspect-js/is-weakmap/commit/b0d7b307d191513ee6fae80dda81db4bfe9ace00)
- Add `related` section to readme [`5644247`](https://github.com/inspect-js/is-weakmap/commit/5644247240a74bc19ea2791f0b609a98a4af5f9f)

## v1.0.0 - 2015-02-18

### Commits

- init [`837063d`](https://github.com/inspect-js/is-weakmap/commit/837063d1ac83ce194eda9135562113c035df4346)
