// Currency utility functions for Indian Rupees (INR)

/**
 * Formats a number as Indian Rupees with proper Indian numbering system
 * @param {number} amount - The amount to format
 * @param {object} options - Formatting options
 * @returns {string} Formatted currency string
 */
export const formatINR = (amount, options = {}) => {
  const {
    showSymbol = true,
    showDecimals = true,
    useIndianNumbering = true
  } = options;

  if (amount === null || amount === undefined || isNaN(amount)) {
    return showSymbol ? '₹0' : '0';
  }

  const numAmount = Number(amount);
  
  if (useIndianNumbering) {
    return formatIndianNumbering(numAmount, showSymbol, showDecimals);
  } else {
    // Standard international formatting with INR
    const formatted = new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: showDecimals ? 2 : 0,
      maximumFractionDigits: showDecimals ? 2 : 0
    }).format(numAmount);
    
    return showSymbol ? formatted : formatted.replace('₹', '').trim();
  }
};

/**
 * Formats number using Indian numbering system (lakhs, crores)
 * @param {number} amount - The amount to format
 * @param {boolean} showSymbol - Whether to show ₹ symbol
 * @param {boolean} showDecimals - Whether to show decimal places
 * @returns {string} Formatted string
 */
const formatIndianNumbering = (amount, showSymbol = true, showDecimals = true) => {
  const absAmount = Math.abs(amount);
  const isNegative = amount < 0;
  
  let formatted = '';
  let suffix = '';
  
  if (absAmount >= 10000000) { // 1 crore and above
    const crores = absAmount / 10000000;
    formatted = crores.toFixed(showDecimals ? 2 : 0);
    suffix = ' Cr';
  } else if (absAmount >= 100000) { // 1 lakh and above
    const lakhs = absAmount / 100000;
    formatted = lakhs.toFixed(showDecimals ? 2 : 0);
    suffix = ' L';
  } else {
    // For amounts less than 1 lakh, use standard formatting
    formatted = new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: showDecimals ? 2 : 0,
      maximumFractionDigits: showDecimals ? 2 : 0
    }).format(absAmount);
  }
  
  // Remove trailing zeros for cleaner display
  if (showDecimals && formatted.includes('.')) {
    formatted = formatted.replace(/\.?0+$/, '');
  }
  
  const result = `${isNegative ? '-' : ''}${showSymbol ? '₹' : ''}${formatted}${suffix}`;
  return result;
};

/**
 * Formats amount for display in tables and lists
 * @param {number} amount - The amount to format
 * @returns {string} Formatted currency string
 */
export const formatCurrencyForDisplay = (amount) => {
  return formatINR(amount, { 
    showSymbol: true, 
    showDecimals: true, 
    useIndianNumbering: true 
  });
};

/**
 * Formats amount for form inputs (without symbol, with decimals)
 * @param {number} amount - The amount to format
 * @returns {string} Formatted number string
 */
export const formatCurrencyForInput = (amount) => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '';
  }
  
  return Number(amount).toFixed(2);
};

/**
 * Parses a currency string and returns the numeric value
 * @param {string} currencyString - The currency string to parse
 * @returns {number} Parsed numeric value
 */
export const parseCurrencyString = (currencyString) => {
  if (!currencyString || typeof currencyString !== 'string') {
    return 0;
  }
  
  // Remove currency symbols, commas, and spaces
  const cleanString = currencyString
    .replace(/[₹,\s]/g, '')
    .replace(/[Ll]$/, '00000') // Convert L (lakh) suffix
    .replace(/[Cc][Rr]$/, '0000000'); // Convert Cr (crore) suffix
  
  const parsed = parseFloat(cleanString);
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Validates if a currency input is valid
 * @param {string|number} value - The value to validate
 * @returns {boolean} Whether the value is valid
 */
export const isValidCurrencyInput = (value) => {
  if (value === '' || value === null || value === undefined) {
    return true; // Allow empty values for optional fields
  }
  
  const numValue = typeof value === 'string' ? parseCurrencyString(value) : Number(value);
  return !isNaN(numValue) && numValue >= 0;
};

/**
 * Gets currency symbol for INR
 * @returns {string} INR currency symbol
 */
export const getCurrencySymbol = () => '₹';

/**
 * Gets currency code for INR
 * @returns {string} INR currency code
 */
export const getCurrencyCode = () => 'INR';

export default {
  formatINR,
  formatCurrencyForDisplay,
  formatCurrencyForInput,
  parseCurrencyString,
  isValidCurrencyInput,
  getCurrencySymbol,
  getCurrencyCode
};
