import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add any auth tokens here if needed
    // const token = localStorage.getItem('authToken');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    const errorMessage = error.response?.data?.message || error.message || 'An error occurred';
    
    // Handle different error types
    if (error.response?.status === 401) {
      // Handle unauthorized access
      console.error('Unauthorized access');
    } else if (error.response?.status === 403) {
      // Handle forbidden access
      console.error('Forbidden access');
    } else if (error.response?.status >= 500) {
      // Handle server errors
      console.error('Server error:', errorMessage);
    }
    
    return Promise.reject({
      message: errorMessage,
      status: error.response?.status,
      data: error.response?.data
    });
  }
);

// Sales Projections API
export const salesProjectionsAPI = {
  // Get all sales projections
  getAll: async (params = {}) => {
    const queryParams = new URLSearchParams();
    
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.offset) queryParams.append('offset', params.offset);
    if (params.employeeId) queryParams.append('employeeId', params.employeeId);
    
    const queryString = queryParams.toString();
    const url = `/sales-projections${queryString ? `?${queryString}` : ''}`;
    
    return await api.get(url);
  },

  // Get sales projection by ID
  getById: async (id) => {
    return await api.get(`/sales-projections/${id}`);
  },

  // Create new sales projection
  create: async (data) => {
    return await api.post('/sales-projections', data);
  },

  // Update sales projection
  update: async (id, data) => {
    return await api.put(`/sales-projections/${id}`, data);
  },

  // Delete sales projection
  delete: async (id) => {
    return await api.delete(`/sales-projections/${id}`);
  },

  // Get projections by employee ID
  getByEmployeeId: async (employeeId) => {
    return await api.get(`/sales-projections?employeeId=${employeeId}`);
  }
};

// Health check
export const healthCheck = async () => {
  try {
    const response = await api.get('/health');
    return response;
  } catch (error) {
    throw error;
  }
};

export default api;
