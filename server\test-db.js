import { testConnection, initSchema, executeQuery } from './config/database.js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config();

const testDatabaseConnection = async () => {
  console.log('🔍 Testing SQLite database connection...');
  console.log('Configuration:');
  console.log(`Database Path: ${process.env.DB_PATH || './database/finance_dashboard.db'}`);
  console.log('');

  try {
    // Test database connection
    const connected = await testConnection();

    if (!connected) {
      throw new Error('Failed to connect to SQLite database');
    }

    console.log('✅ SQLite database connection successful');

    // Check if database file exists
    const dbPath = process.env.DB_PATH || './database/finance_dashboard.db';
    const dbExists = fs.existsSync(dbPath);

    if (dbExists) {
      console.log('✅ Database file exists');

      // Initialize schema (this will create tables if they don't exist)
      const schemaInitialized = await initSchema();

      if (schemaInitialized) {
        console.log('✅ Database schema initialized');

        // Test table existence and data
        const result = await executeQuery('SELECT COUNT(*) as count FROM sales_projections');

        if (result.success) {
          const count = result.data[0].count;
          console.log(`✅ sales_projections table has ${count} records`);

          if (count > 0) {
            const sampleResult = await executeQuery('SELECT * FROM sales_projections LIMIT 1');
            if (sampleResult.success && sampleResult.data.length > 0) {
              console.log('✅ Latest record found:');
              console.log('   Employee:', sampleResult.data[0].employee_name);
              console.log('   Project:', sampleResult.data[0].project);
              console.log('   Amount:', `$${sampleResult.data[0].amount} (${sampleResult.data[0].amount_period})`);
            }
          } else {
            console.log('ℹ️  Table is empty - ready for new data');
          }
        }
      }
    } else {
      console.log('ℹ️  Database file will be created automatically');
    }

    console.log('\n🎉 SQLite database test completed successfully!');
    console.log('\n📋 Database Summary:');
    console.log(`   Type: SQLite`);
    console.log(`   Location: ${path.resolve(dbPath)}`);
    console.log(`   Status: Ready for use`);

  } catch (error) {
    console.error('❌ Database connection failed:');
    console.error('Error:', error.message);
    console.log('\n🔧 Troubleshooting steps:');
    console.log('1. Check if the database directory exists and is writable');
    console.log('2. Verify the DB_PATH in server/.env file');
    console.log('3. Ensure you have sufficient disk space');
    console.log('4. Check file permissions');

    if (error.code === 'ENOENT') {
      console.log('\n💡 Directory does not exist. It will be created automatically.');
    } else if (error.code === 'EACCES') {
      console.log('\n💡 Permission denied. Check file/directory permissions.');
    }
  }
};

testDatabaseConnection();
