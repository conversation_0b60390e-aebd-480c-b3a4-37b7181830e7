{"version": 3, "file": "Statement.js", "sourceRoot": "", "sources": ["../src/Statement.ts"], "names": [], "mappings": ";;;AAEA,uDAAkD;AAElD;;GAEG;AACH,MAAa,SAAS;IAGpB,YAAa,IAAO;QAClB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,IAAI,CAAA;IAClB,CAAC;IAED;;;;;OAKG;IACH,IAAI,CAAE,GAAG,MAAa;QACpB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,CAAC,EAAE;gBAC9B,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;iBAChC;gBAED,OAAO,EAAE,CAAA;YACX,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;OAGG;IACH,KAAK;QACH,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;gBACnB,OAAO,EAAE,CAAA;YACX,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;OAMG;IACH,QAAQ;QACN,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACvB,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;iBAChC;gBAED,OAAO,EAAE,CAAA;YACX,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,GAAG,CAAE,GAAG,MAAa;QACnB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,IAAI,GAAG,IAAI,CAAA;YAEjB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE,UAAU,GAAG;gBACpC,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;iBAChC;gBAED,OAAO,CAAC;oBACN,IAAI;oBACJ,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,GAAG,CAAW,GAAG,MAAa;QAC5B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,EAAE,GAAO,EAAE,EAAE;gBACxC,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;iBAChC;gBAED,OAAO,CAAC,GAAG,CAAC,CAAA;YACd,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,GAAG,CAAa,GAAG,MAAa;QAC9B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,EAAE,IAAQ,EAAE,EAAE;gBACzC,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;iBAChC;gBAED,OAAO,CAAC,IAAI,CAAC,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAyCD,IAAI,CAAW,GAAG,MAAa;QAC7B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,QAAQ,GAA0B,MAAM,CAAC,GAAG,EAAE,CAAA;YAEpD,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;gBAC/C,MAAM,IAAI,KAAK,CACb,oEAAoE,CACrE,CAAA;aACF;YAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACrB,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,EAAE,CAAA;gBAE/B,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE;oBACpC,MAAM,IAAI,KAAK,CACb,4FAA4F,CAC7F,CAAA;iBACF;gBAED,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;aACxB;YAED,IAAI,CAAC,IAAI,CAAC,IAAI,CACZ,GAAG,MAAM,EACT,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACX,IAAI,GAAG,EAAE;oBACP,OAAO,QAAQ,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;iBACxC;gBAED,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;YACrB,CAAC,EACD,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBACb,IAAI,GAAG,EAAE;oBACP,OAAO,MAAM,CAAC,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAA;iBAChC;gBAED,OAAO,CAAC,KAAK,CAAC,CAAA;YAChB,CAAC,CACF,CAAA;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;CACF;AAvOD,8BAuOC"}