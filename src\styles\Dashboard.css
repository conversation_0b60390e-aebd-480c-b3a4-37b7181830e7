/* Dashboard Styles */
.dashboard-container {
  width: 100%;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.dashboard-header h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
}

.dashboard-actions {
  display: flex;
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  font-size: 0.9rem;
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.refresh-btn {
  background-color: transparent;
  color: var(--text-secondary);
}

.refresh-btn:hover {
  background-color: var(--bg-light);
}

.export-btn {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.export-btn:hover {
  background-color: var(--primary-dark);
}

/* Summary Cards */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summary-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: center;
  gap: 1.25rem;
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
}

.summary-card.blue::before {
  background-color: var(--primary-color);
}

.summary-card.green::before {
  background-color: var(--success-color);
}

.summary-card.purple::before {
  background-color: #9c27b0;
}

.summary-card.orange::before {
  background-color: var(--warning-color);
}

.card-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(67, 97, 238, 0.1);
}

.blue .card-icon {
  background-color: rgba(67, 97, 238, 0.1);
}

.green .card-icon {
  background-color: rgba(76, 175, 80, 0.1);
}

.purple .card-icon {
  background-color: rgba(156, 39, 176, 0.1);
}

.orange .card-icon {
  background-color: rgba(255, 152, 0, 0.1);
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.card-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.card-trend {
  font-size: 0.8rem;
  color: var(--success-color);
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.dashboard-chart-container {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.chart-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.chart-period {
  display: flex;
  gap: 0.5rem;
}

.period-btn {
  padding: 0.35rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.period-btn:hover {
  background-color: var(--bg-light);
}

.period-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
}

.chart-message {
  text-align: center;
  color: var(--text-secondary);
}

.chart-icon {
  font-size: 2.5rem;
  display: block;
  margin-bottom: 1rem;
}

/* Activity Section */
.dashboard-activity {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
}

.dashboard-activity h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  gap: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(67, 97, 238, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.activity-details {
  flex: 1;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.activity-action {
  font-weight: 500;
  color: var(--text-primary);
}

.activity-time {
  font-size: 0.8rem;
  color: var(--text-light);
}

.activity-meta {
  display: flex;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.activity-user {
  font-weight: 500;
}

.activity-project::before {
  content: '•';
  margin-right: 0.5rem;
}

.view-all-btn {
  width: 100%;
  padding: 0.75rem;
  margin-top: 1rem;
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--primary-color);
  font-weight: 500;
  transition: var(--transition);
}

.view-all-btn:hover {
  background-color: rgba(67, 97, 238, 0.05);
}

/* Dashboard Footer */
.dashboard-footer {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
}

.quick-actions h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.25rem 1rem;
  background-color: var(--bg-light);
  border: none;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.quick-action-btn:hover {
  background-color: rgba(67, 97, 238, 0.1);
  transform: translateY(-3px);
}

.action-icon {
  font-size: 1.5rem;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .chart-period {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }
}

@media (max-width: 480px) {
  .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    grid-template-columns: 1fr;
  }
}